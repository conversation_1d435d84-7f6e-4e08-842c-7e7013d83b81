﻿using Lrb.Application.Notifications.Dtos;
using Lrb.Application.PushNotification.Web.Specs;
using Lrb.Domain.Constants;

namespace Lrb.Application.PushNotification.Web.Requests
{
    public class SendPushNotificationToUserRequest : IRequest<Response<bool>>
    {
        public Guid UserId { get; set; }
        public string ContactNo { get; set; }
        public string? Name { get; set; }
        public string? Screen { get; set; }
    }
    public class SendPushNotificationToUserRequestHandler : IRequestHandler<SendPushNotificationToUserRequest, Response<bool>>
    {
        private readonly Application.Common.PushNotification.INotificationService _notificationService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<NotificationTracker> _notificationTrackerRepo;
        private readonly IRepositoryWithEvents<Device> _deviceRepo;

        public SendPushNotificationToUserRequestHandler(
                                                      Application.Common.PushNotification.INotificationService notificationService,
                                                      ICurrentUser currentUser,
                                                      IRepositoryWithEvents<NotificationTracker> notificationTrackerRepo,
                                                      IRepositoryWithEvents<Device> deviceRepo
                                                      )
        {
            _currentUser = currentUser;
            _notificationService = notificationService;
            _notificationTrackerRepo = notificationTrackerRepo;
            _deviceRepo = deviceRepo;

        }
        public async Task<Response<bool>> Handle(SendPushNotificationToUserRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var tenantId = _currentUser.GetTenant();
                Notification notification = new();
                List<Notification> notifications = new();
                notification.IsMutableContent = true;
                notification.IsAndroidNotification = true;
                notification.Id = Guid.NewGuid();
                notification.Title = $"Call {request.Name ?? string.Empty}";
                notification.MessageBody = $"{request.ContactNo ?? string.Empty}";
                notification.FCMDeepLinkUrl = ReplaceVariables($"app://com.leadrat.black.mobile.droid/main?&phone=#leadContact#&screen={request.Screen ?? "21"}", request?.ContactNo ?? string.Empty);
                notification.FCMDeepLinkUrl = notification.FCMDeepLinkUrl.Replace(' ', '#');
                var notificationDto = notification.Adapt<NotificationDTO>();
                notificationDto.TenantInfoDto = new()
                {
                    Id = tenantId,
                };
                notificationDto.Action = MessageAction.DEEP_LINK;
                notificationDto.UserIds = new() { request.UserId };
                var deviceInfo = await _deviceRepo.ListAsync(new GetMobileDeviceByUserIdSpec(request.UserId));
                if (request.UserId != Guid.Empty || request.UserId != default && deviceInfo != null)
                {
                    foreach (var device in deviceInfo)
                    {
                        await _notificationService.SendCallNotificationAsync(notificationDto, device.NewNotificationToken ?? string.Empty);
                    }
                }

                NotificationTracker notificationTracker = new()
                {
                    NotificationId = notificationDto.Id,
                    EntityId = request.UserId,
                };
                await _notificationTrackerRepo.AddAsync(notificationTracker);
                return new(true);
            }
            catch (Exception ex) { return new(false); }

        }

        private string ReplaceVariables(string text, string phnNo)
        {
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }
            return text.Replace(NotificationVariables.LeadContact, phnNo);
        }
    }
}
