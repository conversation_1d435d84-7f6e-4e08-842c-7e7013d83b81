﻿using DocumentFormat.OpenXml.Office2010.Excel;
using Lrb.Application.PropertyRefrenceInfomation.Web.Requests;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace Lrb.Application.PropertyRefrenceInfomation.Web.Specs
{
    public class PropertyRefrenceInfosSpecs : EntitiesByPaginationFilterSpec<PropertyReferenceInfo>
    {
        public PropertyRefrenceInfosSpecs(GetAllRefrenceInfoRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.ListingSource)
                .Include(i => i.UserAssignment)
                .Include(i => i.Property)
                .OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));

            if (!string.IsNullOrEmpty(filter.SearchFilter))
            {
                var searchFilter = filter.SearchFilter.ToLower().Trim();

                Query.Where(i => i.ReferenceId.ToLower().Trim().Contains(searchFilter) ||
                                 i.Property.SerialNo.ToLower().Trim().Contains(searchFilter) || 
                                 i.PropertyTitle.ToLower().Trim().Contains(searchFilter));
            }

            if(filter.AssignedUserIds?.Any() ?? false)
            {
                Query.Where(i => EF.Functions.JsonContains(i.UserAssignment.UserIds ?? new(), JsonConvert.SerializeObject(filter.AssignedUserIds.ToArray())));
            }

            if (filter.ListingSourceIds?.Any() ?? false)
            {
                Query.Where(i => filter.ListingSourceIds.Contains(i.ListingSource.Id));
            }

            if(filter.RefrenceIds?.Any() ?? false)
            {
                Query.Where(i => filter.RefrenceIds.Contains(i.ReferenceId));
            }

            if(!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                Query.Where(i => i.PropertyTitle.ToLower().Trim().Contains(filter.PropertyTitle.ToLower().Trim()));
            }

            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                Query.Where(i => i.Property.SerialNo.ToLower().Trim().Contains(filter.SerialNo.ToLower().Trim()));
            }

            if(filter.ListingSourceId != null && filter.ListingSourceId != Guid.Empty)
            {
                Query.Where(i => i.ListingSource.Id == filter.ListingSourceId);
            }

            if(filter.AssociatedProperties?.Any() ?? false)
            {
                Query.Where(i => filter.AssociatedProperties.Contains(i.Property.Id));
            }
        }
    }

    public class GetRefrenceInfoByIdSpecs : Specification<PropertyReferenceInfo>
    {
        public GetRefrenceInfoByIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.ListingSource);
        }

        public GetRefrenceInfoByIdSpecs(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id))
                .Include(i => i.ListingSource);
        }

        public GetRefrenceInfoByIdSpecs(string refIdf, Guid sourceId)
        {
            Query.Where(i => !i.IsDeleted && i.ReferenceId == refIdf && i.ListingSource.Id == sourceId);
        }
    }

    public class RestoreRefrenceInfoByIdSpecs : Specification<PropertyReferenceInfo>
    {
        public RestoreRefrenceInfoByIdSpecs(Guid id)
        {
            Query.Where(i => i.Id == id);
        }

        public RestoreRefrenceInfoByIdSpecs(List<Guid> ids)
        {
            Query.Where(i => ids.Contains(i.Id));
        }
    }
    public class GetAllRefrenceInfosSpecs : Specification<PropertyReferenceInfo>
    {
        public GetAllRefrenceInfosSpecs()
        {
            Query.Where(i => !i.IsDeleted && i.ReferenceId != string.Empty);
        }
    }

    public class GetRefrenceInfoUserAssignmentByIdSpecs : Specification<PropertyReferenceInfo>
    {
        public GetRefrenceInfoUserAssignmentByIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id).Include(i => i.UserAssignment);
        }

        public GetRefrenceInfoUserAssignmentByIdSpecs(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id)).Include(i => i.UserAssignment);
        }
    }

    public class GetAllListingSourceForRefrenceSpecs : Specification<CustomListingSource>
    {
        public GetAllListingSourceForRefrenceSpecs()
        {
            Query.Where(i => !i.IsDeleted && i.ShouldIncludeInRefrenceId == true);
        }
    }

    public class GetAllBulkRefrenceIdUploadTrackerSpecs : EntitiesByPaginationFilterSpec<BulkUploadRefrenceInfoTracker>
    {
        public GetAllBulkRefrenceIdUploadTrackerSpecs(GetBulkRefrenceIdUploadTrackerRequest filter)  : base(filter) 
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
        }
    }

    public class GetAllBulkRefrenceIdUploadByIdSpecs : Specification<BulkUploadRefrenceInfoTracker>
    {
        public GetAllBulkRefrenceIdUploadByIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }

    public class GetAssignmentModuleForReferenceIdSpecs : Specification<AssignmentModule>
    {
        public GetAssignmentModuleForReferenceIdSpecs()
        {
            Query.Where(i => !i.IsDeleted && i.Name == "ReferenceId");
        }
    }

    public class GetUserForReferenceIdAssignmentSpec : Specification<UserView>
    {
        public GetUserForReferenceIdAssignmentSpec()
        {
            Query.Where(i => !i.IsDeleted && i.IsActive);
        }
    }

    public class GetAllRefrenceInfosForBulkUploadSpecs : Specification<PropertyReferenceInfo>
    {
        public GetAllRefrenceInfosForBulkUploadSpecs()
        {
            Query.Where(i => !i.IsDeleted && i.ReferenceId != string.Empty)
                .Include(i => i.ListingSource);
        }
    }

    public class GetRefInfoByIdAnsSourceSpecs : Specification<PropertyReferenceInfo>
    {
        public GetRefInfoByIdAnsSourceSpecs(string refId)
        {
            Query.Where(i => !i.IsDeleted && i.ReferenceId == refId)
                .Include(i => i.UserAssignment);
        }

        public GetRefInfoByIdAnsSourceSpecs(string refId, string portalName)
        {
            Query.Where(i => !i.IsDeleted && i.ReferenceId == refId && i.ListingSource.DisplayName.ToLower().Trim() == portalName.ToLower().Trim())
                .Include(i => i.UserAssignment)
                .Include(i => i.Property)
                .ThenInclude(i => i.MonetaryInfo)
                .Include(i => i.Property)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Property)
                .ThenInclude(i => i.Dimension);
        }
    }
    public class GetPropertySpecs : Specification<Domain.Entities.Property>
    {
        public GetPropertySpecs(string listingId)
        {
            Query.Where(i => !i.IsDeleted && i.PFListingId == listingId)
           .Include(i => i.PropertyAssignments);
        }
    }

    public class GetPropertyByIdForCustomRefSpecs : Specification<Domain.Entities.Property>
    {
        public GetPropertyByIdForCustomRefSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted)
           .Include(i => i.PropertyAssignments)
           .Include(i => i.RefrenceInfos)
           .Include(i => i.PropertyType)
           .Where(i => i.Id == id);
        }
    }
}
