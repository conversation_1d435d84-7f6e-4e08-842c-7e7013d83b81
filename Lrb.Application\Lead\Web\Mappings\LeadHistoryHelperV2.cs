﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Application.Team.Web;
using Lrb.Domain.Entities.MasterData;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;

namespace Lrb.Application.Lead.Web.Mappings
{
    public static class LeadHistoryHelperV2
    {
        // Performance optimizations: Pre-compiled regex and cached reflection data
        private static readonly Regex CamelCaseRegex = new(@"([A-Z])", RegexOptions.Compiled);
        private static readonly ConcurrentDictionary<Type, PropertyInfo[]> PropertyCache = new();
        private static readonly ConcurrentDictionary<string, UserDetailsDto> UserCache = new();

        // Constants for better maintainability
        private static readonly HashSet<string> ExcludedProperties = new()
        {
            "LeadId", "DomainEvents", "AddressId", "Id"
        };

        private static readonly HashSet<string> InvalidValues = new()
        {
            "", "0", "None", "00000000-0000-0000-0000-000000000000"
        };

        private static readonly HashSet<string> AddressProperties = new()
        {
            "City", "State", "Country", "Community", "SubCommunity", "TowerName", "SubLocality"
        };

        // Cached property info for common types
        private static readonly Lazy<PropertyInfo[]> AddressDtoProperties =
            new(() => typeof(AddressDto).GetProperties());

        private static readonly Lazy<PropertyInfo[]> ViewLeadEnquiryDtoProperties =
            new(() => typeof(ViewLeadEnquiryDto).GetProperties());

        // Helper class to reduce parameter passing and improve maintainability
        private class LeadHistoryContext
        {
            public Guid GroupKey { get; set; }
            public int Version { get; set; }
            public Guid LeadId { get; set; }
            public DateTime? ModifiedOn { get; set; }
            public string ModifiedBy { get; set; } = string.Empty;
            public Guid LastModifiedById { get; set; }
            public UserDetailsDto? User { get; set; }
            public Guid CurrentUserId { get; set; }
        }

        // Performance helper methods
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static PropertyInfo[] GetCachedProperties(Type type)
        {
            return PropertyCache.GetOrAdd(type, t => t.GetProperties());
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static async Task<UserDetailsDto?> GetUserWithCaching(IUserService userService, Guid userId, CancellationToken cancellationToken)
        {
            var userKey = userId.ToString();
            if (UserCache.TryGetValue(userKey, out var cachedUser))
                return cachedUser;

            try
            {
                var user = await userService.GetAsync(userKey, cancellationToken);
                if (user != null)
                    UserCache.TryAdd(userKey, user);
                return user;
            }
            catch
            {
                return null;
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static string GetUserFullName(UserDetailsDto? user)
        {
            if (user == null) return string.Empty;
            return $"{user.FirstName ?? string.Empty} {user.LastName ?? string.Empty}".Trim();
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static string FormatFieldName(string fieldName)
        {
            return CamelCaseRegex.Replace(fieldName ?? string.Empty, " $1").Trim();
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static bool IsValidValue(object? value)
        {
            if (value == null) return false;
            var stringValue = value.ToString();
            return !string.IsNullOrEmpty(stringValue) && !InvalidValues.Contains(stringValue);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static bool ShouldIncludeProperty(PropertyInfo property)
        {
            return !ExcludedProperties.Contains(property.Name);
        }

        private static LeadHistoryHot CreateLeadHistoryItem(PropertyInfo property, object propertyValue, LeadHistoryContext context)
        {
            var fieldType = property.PropertyType.Name;
            if (fieldType.Contains("Nullable"))
            {
                fieldType = Nullable.GetUnderlyingType(property.PropertyType)?.Name ?? fieldType;
            }

            return new LeadHistoryHot
            {
                LeadId = context.LeadId,
                ModifiedBy = context.ModifiedBy,
                LastModifiedById = context.LastModifiedById,
                ModifiedOn = context.ModifiedOn,
                GroupKey = context.GroupKey,
                Version = context.Version,
                FieldName = FormatFieldName(property.Name),
                FieldType = fieldType,
                NewValue = propertyValue.ToString(),
                OldValue = null,
                UserId = context.CurrentUserId
            };
        }

        private static async Task<List<LeadHistoryHot>> GetLeadRelatedChildEntityItemsForVMOptimized(
            PropertyInfo property, ViewLeadDto newLead, ViewLeadDto? oldLead,
            List<CustomMasterLeadStatus> statuses, List<MasterPropertyType> propertyTypes,
            LeadHistoryContext context, IUserService userService, CancellationToken cancellationToken)
        {
            var items = new List<LeadHistoryHot>();
            var typeName = property.PropertyType.Name;

            // Use pattern matching for better performance
            switch (typeName)
            {
                case nameof(CustomMasterLeadStatus):
                    items.AddRange(await ProcessStatusChanges(newLead, oldLead, statuses, context));
                    break;
                case nameof(AddressDto):
                    items.AddRange(ProcessAddressChanges(newLead, oldLead, context));
                    break;
                case nameof(UserDto):
                    items.AddRange(ProcessUserChanges(property, newLead, oldLead, context));
                    break;
                case nameof(ViewLeadEnquiryDto):
                    items.AddRange(await ProcessEnquiryChanges(newLead, oldLead, propertyTypes, context, userService, cancellationToken));
                    break;
                default:
                    // Handle specific property names for complex types
                    switch (property.Name)
                    {
                        case nameof(ViewLeadDto.CallRecordingUrls):
                            items.AddRange(ProcessCallRecordingUrlsChanges(newLead, oldLead, context));
                            break;
                        case nameof(ViewLeadDto.Documents):
                            items.AddRange(ProcessDocumentsChanges(newLead, oldLead, context));
                            break;
                        case nameof(ViewLeadDto.ContactRecords):
                            items.AddRange(ProcessContactRecordsChanges(newLead, oldLead, context));
                            break;
                        case nameof(ViewLeadDto.CustomFlags):
                            items.AddRange(ProcessCustomFlagsChanges(newLead, oldLead, context));
                            break;
                        case nameof(ViewLeadDto.Links):
                            items.AddRange(ProcessLinksChanges(newLead, oldLead, context));
                            break;
                        case nameof(ViewLeadDto.GoogleAdsProperties):
                            items.AddRange(ProcessGoogleAdsPropertiesChanges(newLead, oldLead, context));
                            break;
                        default:
                            if (property.PropertyType.IsGenericType)
                            {
                                items.AddRange(ProcessGenericTypeChanges(property, newLead, oldLead, context));
                            }
                            break;
                    }
                    break;
            }

            return items;
        }

        private static async Task<List<LeadHistoryHot>> ProcessStatusChanges(
            ViewLeadDto newLead, ViewLeadDto? oldLead, List<CustomMasterLeadStatus> statuses, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var oldStatus = oldLead?.Status;
            var newStatus = newLead?.Status;

            if (oldStatus == null && newStatus == null) return items;

            // Optimize status lookup with dictionary for O(1) access
            var statusLookup = statuses.ToDictionary(s => s.Id, s => s);

            if (oldStatus != null && newStatus != null && oldStatus.Status != newStatus.Status)
            {
                var oldParentStatus = statusLookup.GetValueOrDefault(oldStatus.BaseId ?? Guid.Empty);
                var newParentStatus = statusLookup.GetValueOrDefault(newStatus.BaseId ?? Guid.Empty);

                if (oldParentStatus != null && newParentStatus != null && oldParentStatus.Status != newParentStatus.Status)
                {
                    items.Add(CreateStatusHistoryItem("Status", oldParentStatus.DisplayName, newParentStatus.DisplayName, context));

                    if (newParentStatus.Level > 0)
                    {
                        items.Add(CreateStatusHistoryItem("Reason", oldStatus.DisplayName, null, context));
                    }
                }
            }
            else if (newStatus != null && oldStatus == null)
            {
                items.Add(CreateStatusHistoryItem("Status", null, newStatus.DisplayName, context));
            }
            else if (oldStatus != null && newStatus == null)
            {
                items.Add(CreateStatusHistoryItem("Status", oldStatus.DisplayName, null, context));
            }

            return items;
        }

        private static LeadHistoryHot CreateStatusHistoryItem(string fieldName, string? oldValue, string? newValue, LeadHistoryContext context)
        {
            return new LeadHistoryHot
            {
                FieldName = fieldName,
                OldValue = oldValue,
                NewValue = newValue,
                FieldType = nameof(CustomMasterLeadStatus),
                LeadId = context.LeadId,
                ModifiedOn = context.ModifiedOn,
                ModifiedBy = context.ModifiedBy,
                LastModifiedById = context.LastModifiedById,
                GroupKey = context.GroupKey,
                Version = context.Version,
                UserId = context.CurrentUserId
            };
        }

        private static List<LeadHistoryHot> ProcessAddressChanges(ViewLeadDto newLead, ViewLeadDto? oldLead, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var oldAddress = oldLead?.Address;
            var newAddress = newLead?.Address;

            if (oldAddress == null && newAddress == null) return items;

            foreach (var property in AddressDtoProperties.Value)
            {
                if (!AddressProperties.Contains(property.Name)) continue;

                var oldValue = oldAddress != null ? property.GetValue(oldAddress)?.ToString() : null;
                var newValue = newAddress != null ? property.GetValue(newAddress)?.ToString() : null;

                if (oldValue != newValue && (IsValidValue(oldValue) || IsValidValue(newValue)))
                {
                    items.Add(new LeadHistoryHot
                    {
                        FieldName = $"Customer Address {property.Name}",
                        OldValue = oldValue,
                        NewValue = newValue,
                        FieldType = nameof(AddressDto),
                        LeadId = context.LeadId,
                        ModifiedBy = context.ModifiedBy,
                        LastModifiedById = context.LastModifiedById,
                        ModifiedOn = context.ModifiedOn,
                        GroupKey = context.GroupKey,
                        Version = context.Version,
                        UserId = context.CurrentUserId
                    });
                }
            }

            return items;
        }

        private static List<LeadHistoryHot> ProcessUserChanges(PropertyInfo property, ViewLeadDto newLead, ViewLeadDto? oldLead, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var fieldName = FormatFieldName(property.Name);

            // Use pattern matching for different user types
            var (oldUser, newUser) = property.Name switch
            {
                nameof(ViewLeadDto.AssignedUser) => (oldLead?.AssignedUser, newLead?.AssignedUser),
                nameof(ViewLeadDto.AssignedFromUser) => (oldLead?.AssignedFromUser, newLead?.AssignedFromUser),
                nameof(ViewLeadDto.LastModifiedByUser) => (oldLead?.LastModifiedByUser, newLead?.LastModifiedByUser),
                nameof(ViewLeadDto.SourcingManagerUser) => (oldLead?.SourcingManagerUser, newLead?.SourcingManagerUser),
                nameof(ViewLeadDto.ClosingManagerUser) => (oldLead?.ClosingManagerUser, newLead?.ClosingManagerUser),
                _ => (null, null)
            };

            if (oldUser?.Id != newUser?.Id && (oldUser != null || newUser != null))
            {
                items.Add(new LeadHistoryHot
                {
                    FieldName = fieldName,
                    OldValue = oldUser?.Name,
                    NewValue = newUser?.Name,
                    FieldType = nameof(UserDto),
                    LeadId = context.LeadId,
                    ModifiedOn = context.ModifiedOn,
                    ModifiedBy = context.ModifiedBy,
                    LastModifiedById = context.LastModifiedById,
                    GroupKey = context.GroupKey,
                    Version = context.Version,
                    UserId = context.CurrentUserId
                });
            }

            return items;
        }

        private static async Task<List<LeadHistoryHot>> ProcessEnquiryChanges(
            ViewLeadDto newLead, ViewLeadDto? oldLead, List<MasterPropertyType> propertyTypes,
            LeadHistoryContext context, IUserService userService, CancellationToken cancellationToken)
        {
            var items = new List<LeadHistoryHot>();
            var oldEnquiry = oldLead?.Enquiry;
            var newEnquiry = newLead?.Enquiry;

            if (oldEnquiry == null && newEnquiry == null) return items;

            foreach (var enquiryProperty in ViewLeadEnquiryDtoProperties.Value)
            {
                if (!ShouldIncludeProperty(enquiryProperty)) continue;

                if (IsComplexTypeProperty(enquiryProperty.PropertyType))
                {
                    var complexItems = await GetLeadEnquiryRelatedChildEntityItemsForVM(
                        enquiryProperty, newEnquiry ?? new(), oldEnquiry, propertyTypes,
                        context.GroupKey, context.Version, context.ModifiedOn ?? DateTime.UtcNow,
                        context.LeadId, userService, cancellationToken, context.User);

                    if (complexItems.Count > 0)
                        items.AddRange(complexItems);
                }
                else
                {
                    var oldValue = oldEnquiry != null ? enquiryProperty.GetValue(oldEnquiry) : null;
                    var newValue = newEnquiry != null ? enquiryProperty.GetValue(newEnquiry) : null;

                    if ((IsValidValue(newValue) || IsValidValue(oldValue)) && !Equals(oldValue, newValue))
                    {
                        items.Add(CreateLeadHistoryItem(enquiryProperty, newValue ?? oldValue!, context));
                    }
                }
            }

            return items;
        }

        private static List<LeadHistoryHot> ProcessGenericTypeChanges(PropertyInfo property, ViewLeadDto newLead, ViewLeadDto? oldLead, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var type = property.PropertyType;

            // Handle different generic collection types efficiently
            if (type.IsGenericType)
            {
                var genericTypeName = type.GetGenericTypeDefinition().Name;
                var elementType = type.GetGenericArguments().FirstOrDefault();

                if (elementType != null)
                {
                    var (oldValue, newValue) = GetCollectionValues(property, newLead, oldLead, elementType);

                    if (oldValue != newValue && (IsValidValue(oldValue) || IsValidValue(newValue)))
                    {
                        items.Add(new LeadHistoryHot
                        {
                            FieldName = property.Name,
                            OldValue = oldValue,
                            NewValue = newValue,
                            FieldType = property.PropertyType.Name,
                            LeadId = context.LeadId,
                            ModifiedBy = context.ModifiedBy,
                            LastModifiedById = context.LastModifiedById,
                            ModifiedOn = context.ModifiedOn,
                            GroupKey = context.GroupKey,
                            Version = context.Version,
                            UserId = context.CurrentUserId
                        });
                    }
                }
            }

            return items;
        }

        private static (string? oldValue, string? newValue) GetCollectionValues(PropertyInfo property, ViewLeadDto newLead, ViewLeadDto? oldLead, Type elementType)
        {
            // Optimize collection value extraction based on element type
            return elementType.Name switch
            {
                nameof(ProjectDto) => (
                    string.Join(",", oldLead?.Projects?.Select(p => p.Name) ?? Enumerable.Empty<string>()),
                    string.Join(",", newLead?.Projects?.Select(p => p.Name) ?? Enumerable.Empty<string>())
                ),
                nameof(PropertyDto) => (
                    string.Join(",", oldLead?.Properties?.Select(p => p.Title) ?? Enumerable.Empty<string>()),
                    string.Join(",", newLead?.Properties?.Select(p => p.Title) ?? Enumerable.Empty<string>())
                ),
                nameof(ChannelPartnerDto) => (
                    string.Join(",", oldLead?.ChannelPartners?.Select(cp => cp.FirmName) ?? Enumerable.Empty<string>()),
                    string.Join(",", newLead?.ChannelPartners?.Select(cp => cp.FirmName) ?? Enumerable.Empty<string>())
                ),
                nameof(AgencyDto) => (
                    string.Join(",", oldLead?.Agencies?.Select(a => a.Name) ?? Enumerable.Empty<string>()),
                    string.Join(",", newLead?.Agencies?.Select(a => a.Name) ?? Enumerable.Empty<string>())
                ),
                nameof(CampaignDto) => (
                    string.Join(",", oldLead?.Campaigns?.Select(c => c.Name) ?? Enumerable.Empty<string>()),
                    string.Join(",", newLead?.Campaigns?.Select(c => c.Name) ?? Enumerable.Empty<string>())
                ),
                nameof(LeadDocumentDto) => (
                    string.Join(",", oldLead?.Documents?.Select(d => d.DocumentName) ?? Enumerable.Empty<string>()),
                    string.Join(",", newLead?.Documents?.Select(d => d.DocumentName) ?? Enumerable.Empty<string>())
                ),
                nameof(CustomFlagDto) => (
                    string.Join(",", oldLead?.CustomFlags?.Select(cf => cf?.Flag?.Name) ?? Enumerable.Empty<string>()),
                    string.Join(",", newLead?.CustomFlags?.Select(cf => cf?.Flag?.Name) ?? Enumerable.Empty<string>())
                ),
                nameof(LinkDto) => (
                    string.Join(",", oldLead?.Links?.Select(l => l.Url) ?? Enumerable.Empty<string>()),
                    string.Join(",", newLead?.Links?.Select(l => l.Url) ?? Enumerable.Empty<string>())
                ),
                _ => (null, null)
            };
        }

        private static List<LeadHistoryHot> ProcessCallRecordingUrlsChanges(ViewLeadDto newLead, ViewLeadDto? oldLead, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var oldUrls = oldLead?.CallRecordingUrls;
            var newUrls = newLead?.CallRecordingUrls;

            if (oldUrls == null && newUrls == null) return items;

            // Compare dictionary counts and keys for meaningful changes
            var oldCount = oldUrls?.SelectMany(x => x.Value.SelectMany(y => y.Value)).Count() ?? 0;
            var newCount = newUrls?.SelectMany(x => x.Value.SelectMany(y => y.Value)).Count() ?? 0;

            if (oldCount != newCount)
            {
                items.Add(new LeadHistoryHot
                {
                    FieldName = "Call Recording URLs",
                    OldValue = oldCount > 0 ? $"{oldCount} recordings" : null,
                    NewValue = newCount > 0 ? $"{newCount} recordings" : null,
                    FieldType = "Dictionary",
                    LeadId = context.LeadId,
                    ModifiedBy = context.ModifiedBy,
                    LastModifiedById = context.LastModifiedById,
                    ModifiedOn = context.ModifiedOn,
                    GroupKey = context.GroupKey,
                    Version = context.Version,
                    UserId = context.CurrentUserId
                });
            }

            return items;
        }

        private static List<LeadHistoryHot> ProcessDocumentsChanges(ViewLeadDto newLead, ViewLeadDto? oldLead, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var oldDocs = oldLead?.Documents;
            var newDocs = newLead?.Documents;

            if (oldDocs == null && newDocs == null) return items;

            var oldDocNames = string.Join(",", oldDocs?.Select(d => d.DocumentName) ?? Enumerable.Empty<string>());
            var newDocNames = string.Join(",", newDocs?.Select(d => d.DocumentName) ?? Enumerable.Empty<string>());

            if (oldDocNames != newDocNames && (IsValidValue(oldDocNames) || IsValidValue(newDocNames)))
            {
                items.Add(new LeadHistoryHot
                {
                    FieldName = "Documents",
                    OldValue = string.IsNullOrEmpty(oldDocNames) ? null : oldDocNames,
                    NewValue = string.IsNullOrEmpty(newDocNames) ? null : newDocNames,
                    FieldType = "List",
                    LeadId = context.LeadId,
                    ModifiedBy = context.ModifiedBy,
                    LastModifiedById = context.LastModifiedById,
                    ModifiedOn = context.ModifiedOn,
                    GroupKey = context.GroupKey,
                    Version = context.Version,
                    UserId = context.CurrentUserId
                });
            }

            return items;
        }

        private static List<LeadHistoryHot> ProcessContactRecordsChanges(ViewLeadDto newLead, ViewLeadDto? oldLead, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var oldRecords = oldLead?.ContactRecords;
            var newRecords = newLead?.ContactRecords;

            if (oldRecords == null && newRecords == null) return items;

            // Compare contact records by creating a summary string
            var oldRecordsValue = oldRecords != null ?
                string.Join(",", oldRecords.Select(kvp => $"{kvp.Key}:{kvp.Value}")) : null;
            var newRecordsValue = newRecords != null ?
                string.Join(",", newRecords.Select(kvp => $"{kvp.Key}:{kvp.Value}")) : null;

            if (oldRecordsValue != newRecordsValue && (IsValidValue(oldRecordsValue) || IsValidValue(newRecordsValue)))
            {
                items.Add(new LeadHistoryHot
                {
                    FieldName = "Contact Records",
                    OldValue = oldRecordsValue,
                    NewValue = newRecordsValue,
                    FieldType = "Dictionary",
                    LeadId = context.LeadId,
                    ModifiedBy = context.ModifiedBy,
                    LastModifiedById = context.LastModifiedById,
                    ModifiedOn = context.ModifiedOn,
                    GroupKey = context.GroupKey,
                    Version = context.Version,
                    UserId = context.CurrentUserId
                });
            }

            return items;
        }

        private static List<LeadHistoryHot> ProcessCustomFlagsChanges(ViewLeadDto newLead, ViewLeadDto? oldLead, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var oldFlags = oldLead?.CustomFlags;
            var newFlags = newLead?.CustomFlags;

            if (oldFlags == null && newFlags == null) return items;

            var oldFlagNames = string.Join(",", oldFlags?.Select(cf => cf?.Flag?.Name) ?? Enumerable.Empty<string>());
            var newFlagNames = string.Join(",", newFlags?.Select(cf => cf?.Flag?.Name) ?? Enumerable.Empty<string>());

            if (oldFlagNames != newFlagNames && (IsValidValue(oldFlagNames) || IsValidValue(newFlagNames)))
            {
                items.Add(new LeadHistoryHot
                {
                    FieldName = "Custom Flags",
                    OldValue = string.IsNullOrEmpty(oldFlagNames) ? null : oldFlagNames,
                    NewValue = string.IsNullOrEmpty(newFlagNames) ? null : newFlagNames,
                    FieldType = "List",
                    LeadId = context.LeadId,
                    ModifiedBy = context.ModifiedBy,
                    LastModifiedById = context.LastModifiedById,
                    ModifiedOn = context.ModifiedOn,
                    GroupKey = context.GroupKey,
                    Version = context.Version,
                    UserId = context.CurrentUserId
                });
            }

            return items;
        }

        private static List<LeadHistoryHot> ProcessLinksChanges(ViewLeadDto newLead, ViewLeadDto? oldLead, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var oldLinks = oldLead?.Links;
            var newLinks = newLead?.Links;

            if (oldLinks == null && newLinks == null) return items;

            var oldLinkNames = string.Join(",", oldLinks?.Select(l => l.Url ?? l.Url) ?? Enumerable.Empty<string>());
            var newLinkNames = string.Join(",", newLinks?.Select(l => l.Url ?? l.Url) ?? Enumerable.Empty<string>());

            if (oldLinkNames != newLinkNames && (IsValidValue(oldLinkNames) || IsValidValue(newLinkNames)))
            {
                items.Add(new LeadHistoryHot
                {
                    FieldName = "Links",
                    OldValue = string.IsNullOrEmpty(oldLinkNames) ? null : oldLinkNames,
                    NewValue = string.IsNullOrEmpty(newLinkNames) ? null : newLinkNames,
                    FieldType = "List",
                    LeadId = context.LeadId,
                    ModifiedBy = context.ModifiedBy,
                    LastModifiedById = context.LastModifiedById,
                    ModifiedOn = context.ModifiedOn,
                    GroupKey = context.GroupKey,
                    Version = context.Version,
                    UserId = context.CurrentUserId
                });
            }

            return items;
        }

        private static List<LeadHistoryHot> ProcessGoogleAdsPropertiesChanges(ViewLeadDto newLead, ViewLeadDto? oldLead, LeadHistoryContext context)
        {
            var items = new List<LeadHistoryHot>();
            var oldProps = oldLead?.GoogleAdsProperties;
            var newProps = newLead?.GoogleAdsProperties;

            if (oldProps == null && newProps == null) return items;

            // Compare Google Ads properties by creating a summary string
            var oldPropsValue = oldProps != null ?
                string.Join(",", oldProps.Select(kvp => $"{kvp.Key}:{kvp.Value}")) : null;
            var newPropsValue = newProps != null ?
                string.Join(",", newProps.Select(kvp => $"{kvp.Key}:{kvp.Value}")) : null;

            if (oldPropsValue != newPropsValue && (IsValidValue(oldPropsValue) || IsValidValue(newPropsValue)))
            {
                items.Add(new LeadHistoryHot
                {
                    FieldName = "Google Ads Properties",
                    OldValue = oldPropsValue,
                    NewValue = newPropsValue,
                    FieldType = "Dictionary",
                    LeadId = context.LeadId,
                    ModifiedBy = context.ModifiedBy,
                    LastModifiedById = context.LastModifiedById,
                    ModifiedOn = context.ModifiedOn,
                    GroupKey = context.GroupKey,
                    Version = context.Version,
                    UserId = context.CurrentUserId
                });
            }

            return items;
        }
        public static async Task<List<LeadHistoryHot>> V2CreateLeadHistoryForVM(ViewLeadDto newLead, ViewLeadDto? oldLead, int version, List<CustomMasterLeadStatus> statuses, List<MasterPropertyType> propertiesType, IUserService userService, Guid currentUserId, CancellationToken cancellationToken)
        {
            if (newLead == null) return new List<LeadHistoryHot>();

            var leadHistories = new List<LeadHistoryHot>();
            var groupKey = Guid.NewGuid();
            var modifiedBy = newLead.LastModifiedBy;

            // Optimize user retrieval with caching
            var user = await GetUserWithCaching(userService, modifiedBy, cancellationToken);
            var userFullName = GetUserFullName(user);

            // Use cached property info for better performance
            var propertyInfo = GetCachedProperties(typeof(ViewLeadDto));

            // Create common history context to avoid repetition
            var historyContext = new LeadHistoryContext
            {
                GroupKey = groupKey,
                Version = version,
                LeadId = newLead.Id,
                ModifiedOn = newLead.LastModifiedOn,
                ModifiedBy = userFullName,
                LastModifiedById = user?.Id ?? Guid.Empty,
                User = user,
                CurrentUserId = currentUserId
            };
            // Process properties more efficiently
            foreach (var property in propertyInfo)
            {
                if (property == null || !ShouldIncludeProperty(property)) continue;

                var propertyValue = property.GetValue(newLead);
                if (propertyValue == null) continue;

                if (IsComplexTypeProperty(property.PropertyType))
                {
                    var complexItems = await GetLeadRelatedChildEntityItemsForVMOptimized(
                        property, newLead, oldLead, statuses, propertiesType, historyContext, userService, cancellationToken);

                    if (complexItems.Count > 0)
                        leadHistories.AddRange(complexItems);
                }
                else if (IsValidValue(propertyValue))
                {
                    var leadHistory = CreateLeadHistoryItem(property, propertyValue, historyContext);
                    leadHistories.Add(leadHistory);
                }
            }
            // Optimize audit stamp assignment
            if (oldLead != null)
            {
                AssignAuditStampsOptimized(leadHistories, userFullName, version);
            }

            return leadHistories;
        }

        private static void AssignAuditStampsOptimized(List<LeadHistoryHot> items, string user, int lastVersion)
        {
            if (items.Count == 0) return;

            var timeStamp = DateTime.UtcNow;
            var newVersion = lastVersion + 1;
            items.ForEach(item => { item.Version = newVersion; item.ModifiedOn = timeStamp; item.ModifiedBy = user; });
        }

        #region Set User View
        public static async Task<ViewLeadDto> SetUserViewForProspectV1(ViewLeadDto lead, List<UserDetailsDto> users, CancellationToken cancellationToken)
        {
            if (lead == null) return null;

            // Create user lookup dictionary for O(1) access instead of O(n) FirstOrDefault calls
            var userLookup = users.ToDictionary(u => u.Id, u => u);

            // Helper method to set user properties efficiently
            void SetUserProperty(Guid? userId, Action<UserDto> setUser)
            {
                if (userId.HasValue && userLookup.TryGetValue(userId.Value, out var user))
                {
                    try
                    {
                        var userDto = user.Adapt<UserDto>();
                        userDto.Name = GetUserFullName(user);
                        setUser(userDto);
                    }
                    catch (NotFoundException) { }
                }
            }

            // Set all user properties efficiently
            SetUserProperty(lead.AssignTo, u => lead.AssignedUser = u);
            SetUserProperty(lead.LastModifiedBy, u => lead.LastModifiedByUser = u);
            SetUserProperty(lead.AssignedFrom, u => lead.AssignedFromUser = u);
            SetUserProperty(lead.SourcingManager, u => lead.SourcingManagerUser = u);
            SetUserProperty(lead.ClosingManager, u => lead.ClosingManagerUser = u);

            return lead;
        }
        #endregion

        #region Get Lead Child Entity

        public static (string, string, string, string) GetPropertiesForProspectHistory(ViewLeadEnquiryDto oldLeadEnquiryDto, ViewLeadEnquiryDto newLeadEnquiryDto, string propertyName)
        {
            string updatedPropertyName = string.Empty;
            string updatedPropertyType = string.Empty;
            string oldProperty = string.Empty;
            string newProperty = string.Empty;
            if (propertyName == "SubLocality")
            {
                updatedPropertyName = "Enquiry SubLocalities";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubLocality)).Select(i => i.SubLocality).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubLocality)).Select(i => i.SubLocality).Distinct().ToList() ?? new());
            }
            if (propertyName == "City")
            {
                updatedPropertyName = "Enquiry Cities";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.City)).Select(i => i.City).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.City)).Select(i => i.City).Distinct().ToList() ?? new());
            }
            if (propertyName == "State")
            {
                updatedPropertyName = "Enquiry States";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.State)).Select(i => i.State).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.State)).Select(i => i.State).Distinct().ToList() ?? new());
            }
            if (propertyName == "Country")
            {
                updatedPropertyName = "Enquiry Countries";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Country)).Select(i => i.Country).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Country)).Select(i => i.Country).Distinct().ToList() ?? new());
            }
            if (propertyName == "BHKTypes")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of BHKTypes";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.BHKTypes?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.BHKTypes?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "Beds")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of Beds";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Beds?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Beds?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "Baths")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of Baths";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Baths?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Baths?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "Floors")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of Floors";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Floors?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Floors?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "BHKs")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of BHKs";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.BHKs?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.BHKs?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "EnquiryTypes")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of EnquiryTypes";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.EnquiryTypes?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.EnquiryTypes?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "TowerName")
            {
                updatedPropertyName = "Enquiry TowerName";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.TowerName)).Select(i => i.TowerName).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.TowerName)).Select(i => i.TowerName).Distinct().ToList() ?? new());
            }
            if (propertyName == "SubCommunity")
            {
                updatedPropertyName = "Enquiry SubCommunity";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubCommunity)).Select(i => i.SubCommunity).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubCommunity)).Select(i => i.SubCommunity).Distinct().ToList() ?? new());
            }
            if (propertyName == "Community")
            {
                updatedPropertyName = "Enquiry Community";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Community)).Select(i => i.Community).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Community)).Select(i => i.Community).Distinct().ToList() ?? new());
            }


            return (updatedPropertyName, updatedPropertyType, oldProperty, newProperty);
        }
        #endregion

        #region Get Lead Enqiry Child Entity
        private static async Task<List<LeadHistoryHot>> GetLeadEnquiryRelatedChildEntityItemsForVM(PropertyInfo property, ViewLeadEnquiryDto newLeadEnquiry, ViewLeadEnquiryDto? oldLeadEnqyiry, List<MasterPropertyType> propertyTypes, Guid groupKey, int version, DateTime lastModifiedOn, Guid leadId, IUserService userService, CancellationToken cancellationToken, UserDetailsDto? user = null)
        {
            var items = new List<LeadHistoryHot>();
            if (property != null)
            {
                var type = property.PropertyType;
                var typeName = type?.Name ?? default;
                if (type != null && type.IsGenericType)
                {
                    if (type.FullName == typeof(IList<EnquiryType>).FullName || type.FullName == typeof(List<EnquiryType>).FullName)
                    {
                        var oldEnquiries = string.Join(",", oldLeadEnqyiry?.EnquiryTypes?.ToList() ?? new());
                        var newEnquiries = string.Join(",", newLeadEnquiry.EnquiryTypes?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var EnquiryItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version,
                                    UserId = user?.Id ?? Guid.Empty
                                };
                                items.Add(EnquiryItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var EnquiryItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version,
                                UserId = user?.Id ?? Guid.Empty
                            };
                            items.Add(EnquiryItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<BHKType>).FullName || type.FullName == typeof(List<BHKType>).FullName)
                    {
                        var oldEnquiries = string.Join(",", oldLeadEnqyiry?.BHKTypes?.ToList() ?? new());
                        var newEnquiries = string.Join(",", newLeadEnquiry?.BHKTypes?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<double>).FullName || type.FullName == typeof(List<double>).FullName)
                    {
                        var oldEnquiries = string.Join(",", oldLeadEnqyiry?.BHKs?.ToList() ?? new());
                        var newEnquiries = string.Join(",", newLeadEnquiry.BHKs?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    if (type.FullName == typeof(IList<int>).FullName || type.FullName == typeof(List<int>).FullName)
                    {
                        var oldEnquiries = string.Empty;
                        var newEnquiries = string.Empty;
                        if(property.Name == "Baths")
                        {
                            oldEnquiries = string.Join(",", oldLeadEnqyiry?.Baths?.ToList() ?? new());
                            newEnquiries = string.Join(",", newLeadEnquiry?.Baths?.ToList() ?? new());
                        }
                        else if (property.Name == "Beds")
                        {
                            oldEnquiries = string.Join(",", oldLeadEnqyiry?.Beds?.ToList() ?? new());
                            newEnquiries = string.Join(",", newLeadEnquiry?.Beds?.ToList() ?? new());
                        }
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    if (type.FullName == typeof(IList<string>).FullName || type.FullName == typeof(List<string>).FullName)
                    {
                        var oldEnquiries = string.Join(",", oldLeadEnqyiry?.Floors?.ToList() ?? new());
                        var newEnquiries = string.Join(",", newLeadEnquiry?.Floors?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    if (type.FullName == typeof(IList<AddressDto>).FullName || type.FullName == typeof(List<AddressDto>).FullName)
                    {
                        var addressProperties = typeof(AddressDto).GetProperties();
                        foreach (var addressProperty in addressProperties)
                        {
                            if (addressProperty.Name == "City" ||
                                       addressProperty.Name == "State" || addressProperty.Name == "SubLocality" || addressProperty.Name == "Country"
                                    || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                    || addressProperty?.Name == "SubLocality")
                            {
                                (string propertyName, string propertyType, string oldValue, string newValue) historyProperties = GetPropertiesForProspectHistory(oldLeadEnqyiry, newLeadEnquiry, addressProperty.Name);
                                if (historyProperties.oldValue != historyProperties.newValue)
                                {
                                    var prospectSource = new LeadHistoryHot()
                                    {
                                        FieldName = historyProperties.propertyName,
                                        OldValue = historyProperties.oldValue,
                                        NewValue = historyProperties.newValue,
                                        FieldType = historyProperties.propertyType,
                                        LeadId = leadId,
                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                        ModifiedOn = lastModifiedOn,
                                        GroupKey = groupKey,
                                        Version = version
                                    };
                                    items.Add(prospectSource);
                                }
                            }
                        }
                    }
                    if (type.FullName == typeof(IList<PropertyTypeDto>).FullName || type.FullName == typeof(List<PropertyTypeDto>).FullName)
                    {
                        var oldParentPropertyType = propertyTypes?.Where(pt => oldLeadEnqyiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                        var newParentPropertyType = propertyTypes?.Where(pt => newLeadEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                        var childOldType = oldLeadEnqyiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                        var childNewType = newLeadEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                        string childOldTypes = null;
                        string childNewTypes = null;
                        if (childOldType != null)
                        {
                            childOldTypes = string.Join(",", childOldType);
                        }
                        if (childNewType != null)
                        {
                            childNewTypes = string.Join(",", childNewType);
                        }
                        if (oldParentPropertyType != null && newParentPropertyType != null)
                        {
                            if (oldParentPropertyType != newParentPropertyType)
                            {
                                var parentPropertyTypeItems = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldParentPropertyType ?? default,
                                    NewValue = newParentPropertyType ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(parentPropertyTypeItems);

                                var childPropertyTypeItems = new LeadHistoryHot()
                                {
                                    FieldName = "SubPropertyType",
                                    OldValue = childOldTypes ?? default,
                                    NewValue = childNewTypes ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(childPropertyTypeItems);
                            }
                        }
                        else if (oldParentPropertyType != null)
                        {
                            var parentPropertyTypeItems = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldParentPropertyType ?? default,
                                NewValue = newParentPropertyType ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(parentPropertyTypeItems);
                            var childPropertyTypeItems = new LeadHistoryHot()
                            {
                                FieldName = "SubPropertyType",
                                OldValue = childOldTypes ?? default,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version,
                            };
                            items.Add(childPropertyTypeItems);
                        }
                        else if (newParentPropertyType != null)
                        {
                            var parentPropertyTypeItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newParentPropertyType ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(parentPropertyTypeItem);
                            var childPropertyTypeItem = new LeadHistoryHot()
                            {
                                FieldName = "SubPropertyType",
                                OldValue = default,
                                NewValue = childNewTypes ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(childPropertyTypeItem);
                        }
                        else
                        {
                            var childPropertyTypeItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldParentPropertyType ?? default,
                                NewValue = newParentPropertyType ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                        }
                    }
                }
            }
            return items;
        }
        #endregion

        #region Check If Property is complex type
        private static readonly HashSet<string> SimpleTypeNames = new()
        {
            nameof(Int64), nameof(Int32), nameof(Int16), nameof(UIntPtr), nameof(UInt32), nameof(Byte), nameof(Char),
            nameof(Half), nameof(Single), nameof(Double), nameof(Decimal), nameof(Boolean),
            nameof(String), nameof(Guid), nameof(DateTime)
        };

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool IsComplexTypeProperty(this Type type)
        {
            // Fast path for simple types
            if (SimpleTypeNames.Contains(type.Name) || type.IsEnum || type.IsValueType)
                return false;

            // Check for nullable types
            var underlyingType = Nullable.GetUnderlyingType(type);
            if (underlyingType != null)
                return IsComplexTypeProperty(underlyingType);

            // Complex types: generic, nested, or reference types (excluding string)
            return type.IsGenericType || type.IsNested || (type.IsClass && type != typeof(string));
        }
        #endregion

        #region Properties to be Included in Lead History
        // This method is kept for backward compatibility but optimized version is used above
        private static bool ShouldIncludePropperty(PropertyInfo property)
        {
            return ShouldIncludeProperty(property);
        }
        #endregion

        #region Audit Stamps
        // This method is kept for backward compatibility but optimized version is used above
        private static void AssignAuditStamps(List<LeadHistoryHot> items, string user, int lastVersion)
        {
            AssignAuditStampsOptimized(items, user, lastVersion);
        }
        #endregion
    }
}
