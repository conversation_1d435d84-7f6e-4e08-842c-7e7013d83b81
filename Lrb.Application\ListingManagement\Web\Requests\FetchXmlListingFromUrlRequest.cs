﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json.Linq;
using System.Xml.Linq;

namespace Lrb.Application.ListingManagement.Web.Requests
{
    public class FetchPFXmlListingFromUrlRequest : IRequest<Response<bool>>
    {
        public string? Url { get; set; }
        public string? TenantId { get; set; }
        public Guid? CurrentUserId { get; set; }
        public Guid? ListingSourecId { get; set; }
    }

    public class FetchXmlListingFromUrlRequestHandler : IRequestHandler<FetchPFXmlListingFromUrlRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<CustomListingSource> _listingSourceRepo;
        private readonly IRepositoryWithEvents<ListingSourceAddress> _listingSourceAddessRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        protected readonly IDapperRepository _dapperRepository;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        protected readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        protected readonly ICurrentUser _currentUser;
        protected readonly IRepositoryWithEvents<Domain.Entities.PropertyAssignment> _propertyAssignmentRepository;
        protected readonly IRepositoryWithEvents<PropertyDimension> _propertyDimensionInfoRepository;
        protected readonly IRepositoryWithEvents<PropertyAmenity> _propertyAmenitiesRepository;
        protected readonly IRepositoryWithEvents<PropertyAttribute> _propertyAttributesRepository;
        protected readonly IRepositoryWithEvents<PropertyOwnerDetails> _propertyOwnerDetailsRepository;
        protected readonly IRepositoryWithEvents<PropertyMonetaryInfo> _propertyMonetaryInfoRepository;
        protected readonly IRepositoryWithEvents<PropertyGallery> _propertyGallleryRepository;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IRepositoryWithEvents<CustomMasterAmenity> _propertyAmenityListRepository;
        protected readonly IRepositoryWithEvents<CustomMasterAttribute> _propertyAttributeListRepository;
        protected readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertyTypeRepository;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        protected readonly IBlobStorageService _blobStorageService;
        protected readonly IRepositoryWithEvents<UserView> _userViewRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingRepo;
        public FetchXmlListingFromUrlRequestHandler(
            IRepositoryWithEvents<ListingSourceAddress> listingSourceAddessRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IDapperRepository dapperRepository,
            ILeadRepositoryAsync leadRepositoryAsync,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IReadRepository<PropertyDimension> propertyDimensionRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<PropertyAssignment> propertyAssignmentRepository,
            IRepositoryWithEvents<PropertyDimension> propertyDimensionInfoRepository,
            IRepositoryWithEvents<PropertyAmenity> propertyAmenitiesRepository,
            IRepositoryWithEvents<PropertyAttribute> propertyAttributesRepository,
            IRepositoryWithEvents<PropertyOwnerDetails> propertyOwnerDetailsRepository,
            IRepositoryWithEvents<PropertyMonetaryInfo> propertyMonetaryInfoRepository,
            IRepositoryWithEvents<PropertyGallery> propertyGallleryRepository,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<CustomMasterAmenity> propertyAmenityListRepository,
            IRepositoryWithEvents<CustomMasterAttribute> propertyAttributeListRepository,
            IRepositoryWithEvents<MasterPropertyType> masterPropertyTypeRepository,
            IRepositoryWithEvents<Domain.Entities.Project> projectsRepo,
            IBlobStorageService blobStorageService,
            IRepositoryWithEvents<UserView> userViewRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingRepo,
            IRepositoryWithEvents<CustomListingSource> listingSourceRepo
            )
        {
            _listingSourceAddessRepo = listingSourceAddessRepo;
            _propertyRepo = propertyRepo;
            _dapperRepository = dapperRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _propertyDimensionRepo = propertyDimensionRepo;
            _currentUser = currentUser;
            _propertyAssignmentRepository = propertyAssignmentRepository;
            _propertyDimensionInfoRepository = propertyDimensionInfoRepository;
            _propertyAmenitiesRepository = propertyAmenitiesRepository;
            _propertyAttributesRepository = propertyAttributesRepository;
            _propertyOwnerDetailsRepository = propertyOwnerDetailsRepository;
            _propertyMonetaryInfoRepository = propertyMonetaryInfoRepository;
            _propertyGallleryRepository = propertyGallleryRepository;
            _addressRepo = addressRepo;
            _propertyAmenityListRepository = propertyAmenityListRepository;
            _propertyAttributeListRepository = propertyAttributeListRepository;
            _masterPropertyTypeRepository = masterPropertyTypeRepository;
            _projectsRepo = projectsRepo;
            _blobStorageService = blobStorageService;
            _userViewRepo = userViewRepo;
            _globalSettingRepo = globalSettingRepo;
            _listingSourceRepo = listingSourceRepo;
        }

        public async Task<Response<bool>> Handle(FetchPFXmlListingFromUrlRequest request, CancellationToken cancellationToken)
        {
            var data = await FetchXmlListingFromUrl(request?.Url ?? string.Empty);

            var pfProperties = ConvertXmlToJson(data).property;

            if (pfProperties?.Any() ?? false)
            {
                var isAdded = await SyncPropertyAsLrbProperty(pfProperties, request?.ListingSourecId ?? Guid.Empty);
                return new(isAdded);
            }
            return new(false);
        }

        #region Fetch Listing From Url
        public async Task<string> FetchXmlListingFromUrl(string url)
        {
            using var httpClient = new HttpClient();
            var response = await httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
        #endregion

        #region Convert Xml to Json
        public PFXmlRootClass ConvertXmlToJson(string xml)
        {
            try
            {
                XDocument doc = XDocument.Parse(xml);

                var properties = doc.Root.Elements("property").Select(p => new PFPropertyXML
                {
                    reference_number = p.Element("reference_number")?.Value,
                    offering_type = p.Element("offering_type")?.Value,
                    property_type = p.Element("property_type")?.Value,
                    price = p.Element("price")?.Element("yearly") != null ? new { yearly = p.Element("price").Element("yearly").Value } : p.Element("price")?.Value,
                    price_on_application = p.Element("price_on_application")?.Value,
                    city = p.Element("city")?.Value,
                    community = p.Element("community")?.Value,
                    sub_community = p.Element("sub_community")?.Value,
                    property_name = p.Element("property_name")?.Value,
                    title_en = p.Element("title_en")?.Value,
                    description_en = p.Element("description_en")?.Value,
                    private_amenities = p.Element("private_amenities")?.Value,
                    size = p.Element("size")?.Value,
                    bedroom = p.Element("bedroom")?.Value,
                    bathroom = p.Element("bathroom")?.Value,
                    agent = p.Element("agent") != null ? new Agent
                    {
                        Id = p.Element("agent").Element("id")?.Value,
                        Name = p.Element("agent").Element("name")?.Value,
                        Email = p.Element("agent").Element("email")?.Value,
                        Phone = p.Element("agent").Element("phone")?.Value,
                        Photo = p.Element("agent").Element("photo")?.Value
                    } : null,
                    completion_status = p.Element("completion_status")?.Value,
                    furnished = p.Element("furnished")?.Value,
                    permit_number = p.Element("permit_number")?.Value,
                    photo = p.Element("photo") != null ? new Photo
                    {
                        url = p.Element("photo").Elements("url").Select(u => u.Value).ToList()
                    } : null,
                    video_tour_url = p.Element("video_tour_url")?.Value,
                    view360 = p.Element("view360")?.Value,
                    plot_size = p.Element("plot_size")?.Value,
                    parking = p.Element("parking")?.Value,
                    availability_date = p.Element("availability_date")?.Value,
                    cheques = p.Element("cheques")?.Value,
                    developer = p.Element("developer") != null ? new List<object> { p.Element("developer").Value } : null
                }).ToList();

                var root = new PFXmlRootClass
                {
                    property = properties
                };
                return root ?? new();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting XML to JSON: {ex.Message}");
                return new();
            }
        }
        #endregion

        #region  Sync Xml Listing as Lrb Listing
        public async Task<bool> SyncPropertyAsLrbProperty(List<PFPropertyXML> pfProperties, Guid listingSourceId)
        {
            try
            {
                #region Fetch All Infomation
                var listingAddresses = await _listingSourceAddessRepo.ListAsync(new GetListingSourceAddressBySourceIdSpecs(listingSourceId));
                var listingSources = await _listingSourceRepo.ListAsync(new GetAllListingSourceByIds(listingSourceId));
                var masterPropertyTypes = await _masterPropertyTypeRepository.ListAsync();
                var masterAmenities = await _propertyAmenityListRepository.ListAsync(new GetAllMasterAminitiesSpecs());
                var masterAreaUnit = await _masterAreaUnitRepo.FirstOrDefaultAsync(new GetMasterAreaUnitByUnitSpecs());
                var masterPropertyAttributes = await _propertyAttributeListRepository.ListAsync(new GetMasterPropertyAttributesSpecs());

                #endregion

                foreach (var property in pfProperties)
                {
                    var createProperty = await CreatePropertyAsync(property, listingSources, listingAddresses, masterPropertyTypes);
                    createProperty = await _propertyRepo.AddAsync(createProperty);

                    #region Property Amenity
                    List<string> amenities = new();
                    List<string> privateAmenities = new();
                    List<string> comersialAmenities = new();
                    if (!string.IsNullOrEmpty(property.private_amenities))
                    {
                        privateAmenities = property.private_amenities.Split(",").ToList();
                    }
                    if (!string.IsNullOrEmpty(property.commercial_amenities))
                    {
                        comersialAmenities = property.commercial_amenities.Split(",").ToList();
                    }
                    if ((privateAmenities?.Any() ?? false) && (comersialAmenities?.Any() ?? false))
                    {
                        amenities = privateAmenities.Union(comersialAmenities).ToList();
                    }
                    else if (privateAmenities?.Any() ?? false)
                    {
                        amenities = privateAmenities;
                    }
                    else if (comersialAmenities?.Any() ?? false)
                    {
                        amenities = comersialAmenities;
                    }

                    if (amenities?.Any() ?? false)
                    {
                        List<PropertyAmenity> lrbAmenities = new();
                        foreach (var ame in amenities)
                        {
                            CustomMasterAmenity? masterAmenity = null;

                            #region Amenitites Short form
                            switch (ame)
                            {
                                case "CP":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Covered Parking").FirstOrDefault();
                                    break;
                                case "BK":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Built-in Kitchen Appliances").FirstOrDefault();
                                    break;
                                case "PA":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Pets Allowed").FirstOrDefault();
                                    break;
                                case "SP":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Shared Pool").FirstOrDefault();
                                    break;
                                case "VW":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "View of Water").FirstOrDefault();
                                    break;
                                case "BA":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Balcony").FirstOrDefault();
                                    break;
                                case "BW":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Built-in Wardrobes").FirstOrDefault();
                                    break;
                                case "AC":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Central A/C & Heating").FirstOrDefault();
                                    break;
                                case "CS":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Concierge Service").FirstOrDefault();
                                    break;
                                case "PY":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Private Gym").FirstOrDefault();
                                    break;
                                case "PP":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Private Pool").FirstOrDefault();
                                    break;
                                case "SE":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Security").FirstOrDefault();
                                    break;
                                case "SY":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Shared Gym").FirstOrDefault();
                                    break;
                                case "SS":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Shared Spa").FirstOrDefault();
                                    break;
                                case "ST":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Study").FirstOrDefault();
                                    break;
                                case "WC":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Walk-in Closet").FirstOrDefault();
                                    break;
                                case "CR":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Conference Room").FirstOrDefault();
                                    break;
                                case "AN":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Available Networked").FirstOrDefault();
                                    break;
                                case "DN":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Dining in building").FirstOrDefault();
                                    break;
                                case "LB":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Lobby in Building").FirstOrDefault();
                                    break;
                                case "VC":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Vastu-compliant").FirstOrDefault();
                                    break;
                                case "MZ":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Mezzanine").FirstOrDefault();
                                    break;
                                case "PN":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Pantry").FirstOrDefault();
                                    break;
                                case "BL":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "View of Landmark").FirstOrDefault();
                                    break;
                                case "MR":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Maid Room").FirstOrDefault();
                                    break;
                                case "MS":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Maid Service").FirstOrDefault();
                                    break;
                                case "PG":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Private Garden").FirstOrDefault();
                                    break;
                                case "PJ":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Private Jacuzzi").FirstOrDefault();
                                    break;
                                case "CO":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Children Pool").FirstOrDefault();
                                    break;
                                case "PR":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Children PlayArea").FirstOrDefault();
                                    break;
                                case "BR":
                                    masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName == "Barbecue Area").FirstOrDefault();
                                    break;
                            }
                            #endregion

                            if (masterAmenity != null)
                            {
                                PropertyAmenity amenity = new()
                                {
                                    PropertyId = createProperty.Id,
                                    MasterPropertyAmenityId = masterAmenity.Id,
                                };

                                lrbAmenities.Add(amenity);
                            }
                        }
                        if (lrbAmenities?.Any() ?? false)
                        {
                            await _propertyAmenitiesRepository.AddRangeAsync(lrbAmenities);
                        }
                    }
                    #endregion

                    #region Property Dimension
                    if ((!string.IsNullOrEmpty(property?.size)) || (!string.IsNullOrEmpty(property?.plot_size)))
                    {
                        PropertyDimension? dimension = null;
                        var areaSize = double.TryParse(property.size, out double area);
                        var carepetArea = double.TryParse(property.plot_size, out double plotSize);
                        if ((areaSize) || (carepetArea))
                        {
                            dimension = new()
                            {
                                Area = area,
                                AreaUnitId = masterAreaUnit?.Id ?? Guid.Empty,
                                CarpetArea = plotSize,
                                CarpetAreaId = masterAreaUnit?.Id ?? Guid.Empty,
                                PropertyId = createProperty.Id,
                            };
                            await _propertyDimensionInfoRepository.AddAsync(dimension);
                        }
                    }
                    #endregion

                    #region Property Arributes
                    if (!string.IsNullOrWhiteSpace(property?.bedroom))
                    {
                        var attribute = (masterPropertyAttributes.Where(i => i.AttributeName == "numberOfBedrooms")).FirstOrDefault();
                        PropertyAttribute propertyArribute = new()
                        {
                            MasterPropertyAttributeId = attribute?.Id ?? Guid.Empty,
                            PropertyId = createProperty.Id,
                            Value = property?.bedroom
                        };
                        await _propertyAttributesRepository.AddAsync(propertyArribute);
                    }
                    if (!string.IsNullOrWhiteSpace(property?.bathroom))
                    {
                        var attribute = (masterPropertyAttributes.Where(i => i.AttributeName == "numberOfBathrooms")).FirstOrDefault();
                        PropertyAttribute propertyArribute = new()
                        {
                            MasterPropertyAttributeId = attribute?.Id ?? Guid.Empty,
                            PropertyId = createProperty.Id,
                            Value = property?.bathroom
                        };
                        await _propertyAttributesRepository.AddAsync(propertyArribute);
                    }
                    if (!string.IsNullOrWhiteSpace(property?.floor))
                    {
                        var attribute = (masterPropertyAttributes.Where(i => i.AttributeName == "numberOfFloors")).FirstOrDefault();
                        PropertyAttribute propertyArribute = new()
                        {
                            MasterPropertyAttributeId = attribute?.Id ?? Guid.Empty,
                            PropertyId = createProperty.Id,
                            Value = property?.floor
                        };
                        await _propertyAttributesRepository.AddAsync(propertyArribute);
                    }
                    if (!string.IsNullOrWhiteSpace(property?.parking))
                    {
                        var attribute = (masterPropertyAttributes.Where(i => i.AttributeName == "numberOfCarParking")).FirstOrDefault();
                        PropertyAttribute propertyArribute = new()
                        {
                            MasterPropertyAttributeId = attribute?.Id ?? Guid.Empty,
                            PropertyId = createProperty.Id,
                            Value = property?.parking
                        };
                        await _propertyAttributesRepository.AddAsync(propertyArribute);
                    }
                    #endregion

                    #region Property Gallery
                    var imageUrls = (property?.photo?.url.Select(i => i))?.ToList();
                    if (imageUrls?.Any() ?? false)
                    {
                        var keys = await UploadImageInS3(imageUrls);
                        List<PropertyGallery> propertyGalleries = new();
                        foreach (var key in keys)
                        {
                            PropertyGallery propertyGallery = new()
                            {
                                ImageKey = "photo",
                                ImageFilePath = key,
                                PropertyId = createProperty.Id,
                                IsCoverImage = false,
                                Name = $"PF {GenerateRandomFileName()}",
                                GalleryType = PropertyGalleryType.Image,
                            };
                            propertyGalleries.Add(propertyGallery);
                        }

                        if (propertyGalleries?.Any() ?? false)
                        {
                            await _propertyGallleryRepository.AddRangeAsync(propertyGalleries);
                        }
                    }

                    if (!string.IsNullOrEmpty(property?.video_tour_url))
                    {
                        var key = await UploadVideosInS3(property?.video_tour_url ?? string.Empty);
                        if (!string.IsNullOrEmpty(key))
                        {
                            PropertyGallery propertyGallery = new()
                            {
                                ImageKey = "video",
                                ImageFilePath = key,
                                PropertyId = createProperty.Id,
                                IsCoverImage = false,
                                Name = $"PF {GenerateRandomFileName()}",
                                GalleryType = PropertyGalleryType.Video,
                            };
                            await _propertyGallleryRepository.AddAsync(propertyGallery);
                        }
                    }
                    #endregion
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<Domain.Entities.Property> CreatePropertyAsync(PFPropertyXML listedProperty, List<CustomListingSource> listingSources, List<ListingSourceAddress> listingAddresses, List<MasterPropertyType> masterPropertyTypes)
        {
            var property = new Domain.Entities.Property()
            {
                Title = listedProperty.title_en,
                AboutProperty = listedProperty?.description_en,
                Status = PropertyStatus.Active,
                ShouldVisisbleOnListing = true,
                PermitNumber = listedProperty?.permit_number,
                ListingSources = listingSources,
                ListingStatus = ListingStatus.Approved,

            };

            if (!string.IsNullOrEmpty(listedProperty?.view360))
            {
                property.View360Url = new List<string>() { listedProperty?.view360 ?? string.Empty };
            }

            if (!string.IsNullOrWhiteSpace(listedProperty?.bedroom))
            {
                if (listedProperty?.bedroom.ToLower().Trim() == "studio")
                {
                    property.NoOfBHKs = 0.5;
                }
                else
                {
                    var isConverted = int.TryParse(listedProperty?.bedroom, out var value);
                    if (isConverted)
                    {
                        property.NoOfBHKs = value;
                    }
                }
            }

            switch (listedProperty?.completion_status)
            {
                case "completed":
                    property.CompletionStatus = CompletionStatus.Completed;
                    property.OfferingType = OfferingType.Ready;
                    break;
                case "off_plan":
                    property.CompletionStatus = CompletionStatus.OffPlan;
                    property.OfferingType = OfferingType.OffPlan;
                    break;
                case "off_plan_primary":
                    property.CompletionStatus = CompletionStatus.OffPlanPrimary;
                    break;
                case "completed_primary":
                    property.CompletionStatus = CompletionStatus.CompletedPrimary;
                    break;
            }

            switch (listedProperty?.offering_type)
            {
                case "RS":
                    property.EnquiredFor = EnquiryType.Sale;
                    break;
                case "RR":
                    property.EnquiredFor = EnquiryType.Rent;
                    break;
                case "CS":
                    property.EnquiredFor = EnquiryType.Sale;
                    break;
                case "CR":
                    property.EnquiredFor = EnquiryType.Rent;
                    break;
            }

            property.FurnishStatus = listedProperty?.furnished == "Yes" ? FurnishStatus.Furnished : listedProperty?.furnished == "No" ? FurnishStatus.Unfurnished : listedProperty?.furnished == "Partly" ? FurnishStatus.Semifurnished : FurnishStatus.None;

            #region Monetory Info
            if (listedProperty?.price != null)
            {
                if (listedProperty?.price is string stringPrice)
                {
                    var isParsed = long.TryParse(stringPrice, out long price);
                    var isCheckParsed = int.TryParse(listedProperty.cheques, out int cheques);
                    if (isParsed)
                    {
                        var monetoryInfo = new PropertyMonetaryInfo()
                        {
                            ExpectedPrice = price,
                            IsPriceVissible = listedProperty.price_on_application?.ToLower().Trim() == "yes" ? true : false,
                            NoOfChequesAllowed = isCheckParsed ? cheques : null
                        };

                        property.MonetaryInfo = monetoryInfo;
                    }
                }
                else if (listedProperty?.price is JObject jObjectPrice)
                {
                    var jsonPrice = jObjectPrice["yearly"]?.ToString();
                    var isParsed = long.TryParse(jsonPrice, out long price);
                    if (isParsed)
                    {
                        var monetoryInfo = new PropertyMonetaryInfo()
                        {
                            ExpectedPrice = price,
                            IsPriceVissible = listedProperty.price_on_application?.ToLower().Trim() == "yes" ? true : false
                        };

                        property.MonetaryInfo = monetoryInfo;
                    }
                }
            }
            #endregion

            #region Projects
            if (!string.IsNullOrWhiteSpace(listedProperty?.project_name))
            {
                Domain.Entities.Project project = new();
                Lrb.Domain.Entities.Project? existingProject = (await _projectsRepo.ListAsync(new GetProjectsByNameSpecsV2(listedProperty?.project_name ?? string.Empty))).FirstOrDefault();
                if (existingProject != null)
                {
                    property.Project = existingProject;
                }
                else
                {
                    Lrb.Domain.Entities.Project tempProjects = new() { Name = listedProperty?.project_name ?? string.Empty };
                    tempProjects = await _projectsRepo.AddAsync(tempProjects);
                    property.Project = tempProjects;
                }
            }
            #endregion

            #region Address
            if (
               (!string.IsNullOrWhiteSpace(listedProperty?.community)) ||
               (!string.IsNullOrWhiteSpace(listedProperty?.city)) ||
               (!string.IsNullOrWhiteSpace(listedProperty?.sub_community)) ||
               (!string.IsNullOrWhiteSpace(listedProperty?.property_name))
              )
            {
                ListingSourceAddress listingAddress = new();
                if (!string.IsNullOrWhiteSpace(listedProperty?.property_name))
                {
                    var address = listingAddresses.Where(i =>
                        i.TowerName.ToLower().Trim() == listedProperty?.property_name?.ToLower().Trim() &&
                        i.Community.ToLower().Trim() == listedProperty?.community?.ToLower().Trim() &&
                        i.SubCommunity.ToLower().Trim() == listedProperty.sub_community?.ToLower().Trim() &&
                        i.City.ToLower().Trim() == listedProperty?.city?.ToLower().Trim()).FirstOrDefault();

                    if (address == null)
                    {
                        var newAddress = new ListingSourceAddress()
                        {
                            TowerName = listedProperty?.property_name,
                            SubCommunity = listedProperty?.sub_community,
                            Community = listedProperty?.community,
                            City = listedProperty?.city,
                            ListingSource = listingSources.FirstOrDefault(),
                        };

                        newAddress = await _listingSourceAddessRepo.AddAsync(newAddress);

                        property.ListingSourceAddresses = new List<ListingSourceAddress>() { newAddress };
                    }
                    else
                    {
                        property.ListingSourceAddresses = new List<ListingSourceAddress>() { address };
                    }
                }
                else
                {
                    var address = listingAddresses.Where(i =>
                        i.Community.ToLower().Trim() == listedProperty?.community?.ToLower().Trim() &&
                        i.SubCommunity.ToLower().Trim() == listedProperty.sub_community?.ToLower().Trim() &&
                        i.City.ToLower().Trim() == listedProperty?.city?.ToLower().Trim()).FirstOrDefault();

                    if (address == null)
                    {
                        var newAddress = new ListingSourceAddress()
                        {
                            TowerName = listedProperty?.property_name,
                            SubCommunity = listedProperty?.sub_community,
                            Community = listedProperty?.community,
                            City = listedProperty?.city,
                            ListingSource = listingSources.FirstOrDefault(),
                        };

                        newAddress = await _listingSourceAddessRepo.AddAsync(newAddress);

                        property.ListingSourceAddresses = new List<ListingSourceAddress>() { newAddress };
                    }
                    else
                    {
                        property.ListingSourceAddresses = new List<ListingSourceAddress>() { address };
                    }
                }
            }
            #endregion

            #region Property Type
            if (!string.IsNullOrWhiteSpace(listedProperty?.property_type))
            {
                MasterPropertyType? masterPropertyType = null;
                switch (listedProperty.property_type)
                {
                    case "AP":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Apartment").FirstOrDefault();
                        break;
                    case "CD":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Compound").FirstOrDefault();
                        break;
                    case "BW":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Bungalow").FirstOrDefault();
                        break;
                    case "BU":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Bulk Units").FirstOrDefault();
                        break;
                    case "FA":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Factory").FirstOrDefault();
                        break;
                    case "FM":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Farm").FirstOrDefault();
                        break;
                    case "FF":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Full Floor").FirstOrDefault();
                        break;
                    case "HF":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Half Floor").FirstOrDefault();
                        break;
                    case "LP":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Plot").FirstOrDefault();
                        break;
                    case "OF":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Office Space").FirstOrDefault();
                        break;
                    case "PH":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Penthouse").FirstOrDefault();
                        break;
                    case "RE":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Retail").FirstOrDefault();
                        break;
                    case "SA":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Staff Accommodation").FirstOrDefault();
                        break;
                    case "SH":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Shop").FirstOrDefault();
                        break;
                    case "CW":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Showroom").FirstOrDefault();
                        break;
                    case "TH":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Townhouse").FirstOrDefault();
                        break;
                    case "VH":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Villa").FirstOrDefault();
                        break;
                    case "WB":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Complete Building").FirstOrDefault();
                        break;
                    case "DX":
                        masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == "Duplex").FirstOrDefault();
                        break;
                }

                if (masterPropertyType != null)
                {
                    property.PropertyType = masterPropertyType;
                }
            }
            #endregion

            #region User Assignment
            if (listedProperty?.agent != null)
            {
                var lrbUser = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(listedProperty.agent.Name));
                List<PropertyAssignment> propertyAssignments = new();
                if (lrbUser != null)
                {
                    PropertyAssignment propertyAssigned = new();
                    propertyAssigned.AssignedTo = lrbUser.Id;
                    propertyAssigned.AssignedUser = lrbUser.FirstName + " " + lrbUser.LastName;
                    propertyAssigned.IsCurrentlyAssigned = true;
                    propertyAssignments.Add(propertyAssigned);
                    property.PropertyAssignments = propertyAssignments;
                }
            }
            #endregion

            return property;
        }
        #endregion

        #region Upload Property Image In S3
        public async Task<List<string>> UploadImageInS3(List<string> imageUrl)
        {
            List<string> keys = new List<string>();
            try
            {
                foreach (var url in imageUrl)
                {
                    var client = new HttpClient();
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    var response = await client.SendAsync(request);
                    response.EnsureSuccessStatusCode();
                    var data = await response.Content.ReadAsByteArrayAsync();

                    using (var stream = new MemoryStream(data))
                    {
                        var key = await _blobStorageService.UploadObjectAsync("leadrat-black", "Images/PropertyImages", GenerateRandomFileName(), stream);
                        keys.Add(key);
                    }
                }
                return keys;
            }
            catch (Exception ex)
            {
                return keys;
            }
        }

        public string GenerateRandomFileName()
        {
            string datePart = DateTime.Now.ToString("ddMMyyyy");
            Random random = new Random();
            string randomPart = random.Next(100000000, 999999999).ToString() +
                                random.Next(100000000, 999999999).ToString();

            return datePart + randomPart;
        }

        public async Task<string> UploadVideosInS3(string videoUrl)
        {
            string key = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(videoUrl))
                {
                    var client = new HttpClient();
                    var request = new HttpRequestMessage(HttpMethod.Get, videoUrl);
                    var response = await client.SendAsync(request);
                    response.EnsureSuccessStatusCode();
                    var data = await response.Content.ReadAsByteArrayAsync();

                    using (var stream = new MemoryStream(data))
                    {
                        key = await _blobStorageService.UploadObjectAsync("qleadrat-black", "Images/PropertyImages", GenerateRandomFileName(), stream);
                    }
                }
                return key;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }
        #endregion
    }
}
