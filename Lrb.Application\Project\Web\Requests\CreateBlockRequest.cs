﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.CustomForm.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Project.Web
{
    public class CreateBlockRequest : CreateBlockDto, IRequest<Response<Guid>>
    {
    }
    public class CreateBlockRequestHandler : IRequestHandler<CreateBlockRequest, Response<Guid>>
    {
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Block> _blockRepo;
        private readonly IRepositoryWithEvents<UnitType> _unitTypeRepo;
        private readonly IGooglePlacesService _googlePlacesService;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFieldValue> _customFormValueRepo;

        public CreateBlockRequestHandler(
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<PropertyDimension> dimensionRepo,
            IReadRepository<MasterPropertyType> masterPropertytypeRepo,
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Block> blockRepo,
            IRepositoryWithEvents<UnitType> unitTypeRepo,
            IGooglePlacesService googlePlacesService,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<Domain.Entities.CustomFieldValue> customFormValueRepo,
            IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo
            )
        {
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _addressRepo = addressRepo;
            _projectRepo = projectRepo;
            _blockRepo = blockRepo;
            _unitTypeRepo = unitTypeRepo;
            _googlePlacesService = googlePlacesService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customFormValueRepo = customFormValueRepo;
            _customFormRepo = customFormRepo;
        }

        public async Task<Response<Guid>> Handle(CreateBlockRequest request, CancellationToken cancellationToken)
        {
            var project = await _projectRepo.GetByIdAsync(request.ProjectId, cancellationToken);
            if (project == null)
            {
                throw new NotFoundException("No Project found by the id.");
            }
            Block block = request.Adapt<Block>();
            block.ProjectId = request.ProjectId;
            try
            {
                block = await _blockRepo.AddAsync(block);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBlockRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            if (request?.CustomFields?.Any() == true)
            {
                await SaveCustomFieldValues(block.Id, request.CustomFields, request.ProjectId,cancellationToken);
            }
            return new Response<Guid>(block.Id);

        }
        private async Task SaveCustomFieldValues(Guid blockId, List<FormFieldValueDto> customFields, Guid projectId, CancellationToken cancellationToken)
        {
            var formFieldIds = customFields.Select(f => f.FormFieldId ?? Guid.Empty).Distinct().ToList();
            var existingFields = (await _customFormRepo.ListAsync(new GetCustomFormFields(formFieldIds), cancellationToken)).ToDictionary(f => f.Id, f => f); ;
            var formFieldValues = customFields
                .Where(fv => fv.FormFieldId.HasValue && existingFields.ContainsKey(fv.FormFieldId.Value))
                .Select(fv => new CustomFieldValue
                {
                    Id = Guid.NewGuid(),
                    FormFieldId = fv.FormFieldId.Value,
                    EntityId = projectId,
                    Value = fv.Value,
                    IsDeleted = false,
                    EntityChildId = blockId
                })
                .ToList();

            await _customFormValueRepo.AddRangeAsync(formFieldValues, cancellationToken);

        }
    }
}
