﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Amazon.Extensions.CognitoAuthentication" Version="2.2.3" />
    <PackageReference Include="Amazon.Lambda.Core" Version="2.1.0" />
    <PackageReference Include="Ardalis.Specification.EntityFrameworkCore" Version="6.1.0" />
    <PackageReference Include="AWSSDK.Batch" Version="3.7.102.49" />
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.200.3" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.4" />
    <PackageReference Include="AWSSDK.Lambda" Version="3.7.105.6" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.9.71" />
    <PackageReference Include="AWSSDK.CognitoIdentityProvider" Version="3.7.101.6" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.102.41" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.1" />
    <PackageReference Include="ClosedXML" Version="0.96.0" />
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="Facebook" Version="6.0.10" />
    <PackageReference Include="Finbuckle.MultiTenant" Version="6.8.1" />
    <PackageReference Include="Finbuckle.MultiTenant.AspNetCore" Version="6.8.1" />
    <PackageReference Include="Finbuckle.MultiTenant.EntityFrameworkCore" Version="6.8.1" />
    <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
    <PackageReference Include="GoogleApi" Version="4.2.6" />
    <PackageReference Include="Hangfire" Version="1.8.2" />
    <PackageReference Include="Hangfire.Console" Version="1.4.2" />
    <PackageReference Include="Hangfire.Console.Extensions" Version="1.0.5" />
    <PackageReference Include="Hangfire.Dashboard.Basic.Authentication" Version="5.0.0" />
    <PackageReference Include="Hangfire.PostgreSql" Version="1.19.12" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.71" />
    <PackageReference Include="ISO3166" Version="1.0.4" />
    <PackageReference Include="LinqKit" Version="1.3.8" />
    <PackageReference Include="MailKit" Version="3.4.1" />
    <PackageReference Include="Mapster" Version="7.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="6.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="6.0.10" />
    <PackageReference Include="NetEscapades.AspNetCore.SecurityHeaders" Version="0.21.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.StackExchangeRedis" Version="6.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="6.0.10" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="6.0.10" />
    <PackageReference Include="Microsoft.Graph" Version="4.48.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="1.25.3" />
    <PackageReference Include="MimeKit" Version="3.4.1" />
    <PackageReference Include="Nager.Country" Version="4.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="6.0.7" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL.Design" Version="1.1.0" />
    <PackageReference Include="NSwag.AspNetCore" Version="13.17.0" />
    <PackageReference Include="RazorEngineCore" Version="2022.8.1" />
    <PackageReference Include="RestSharp" Version="108.0.2" />
    <PackageReference Include="Serilog" Version="2.12.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="SqlKata" Version="2.4.0" />
    <PackageReference Include="SqlKata.Execution" Version="2.4.0" />
    <PackageReference Include="TimeZoneConverter" Version="6.1.0" />
	<PackageReference Include="VimeoDotNet" Version="3.3.0" />
    <PackageReference Include="ZymLabs.NSwag.FluentValidation.AspNetCore" Version="0.5.1" />
	<PackageReference Include="AWSSDK.SecretsManager" Version="3.7.100.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Lrb.Application\Lrb.Application.csproj" />
  </ItemGroup>

</Project>
