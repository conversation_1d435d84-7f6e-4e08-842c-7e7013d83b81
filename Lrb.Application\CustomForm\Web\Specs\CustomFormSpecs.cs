using Lrb.Application.CustomForm.Web.Requests;

namespace Lrb.Application.CustomForm.Web.Specs
{
    public class GetCustomFormValueByIdSpecs : Specification<Domain.Entities.CustomFieldValue>
    {
        public GetCustomFormValueByIdSpecs(Guid fieldId)
        {
            Query.Where(i => i.FormFieldId == fieldId && !i.IsDeleted);
        }

    }
    public class GetAllCustomFormSpecs : EntitiesByPaginationFilterSpec<Domain.Entities.CustomFormFields>
    {
        public GetAllCustomFormSpecs(GetAllCustomFormRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                 .OrderBy(i => i.LastModifiedOn);
        }
    }
    public class GetAllCustomFormCountSpecs : Specification<Domain.Entities.CustomFormFields>
    {
        public GetAllCustomFormCountSpecs(GetAllCustomFormRequest request)
        {
            Query.Where(i => !i.IsDeleted);
        }

        public GetAllCustomFormCountSpecs()
        {
            Query.Where(i => !i.IsDeleted);
        }
        public GetAllCustomFormCountSpecs(Guid entityChildId, string module)
        {
            Query.Where(i => !i.IsDeleted && entityChildId == i.EntityChildId && i.Module == module);
        }
    }
    public class GetUnitsByFieldIdsSpec : Specification<CustomFieldValue>
    {
        public GetUnitsByFieldIdsSpec(Guid projectId, List<Guid> formFieldIds, Guid entityChildId)
        {
            Query.Where(i => !i.IsDeleted && i.EntityId == projectId && formFieldIds.Contains(i.FormFieldId) && i.EntityChildId == entityChildId);
        }
    }
    public class GetCustomFormFields : Specification<CustomFormFields>
    {
        public GetCustomFormFields(List<Guid> formFieldId)
        {
            Query.Where(i => !i.IsDeleted && formFieldId.Contains(i.Id));
        }
    }

}
