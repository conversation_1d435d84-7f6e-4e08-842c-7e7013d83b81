﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Shared.Extensions;
using Serilog;

namespace Lrb.Application.Lead.Web.Requests
{
    public class UpdateSiteVisitOrMeetingDoneRequest : IRequest<Response<bool>>
    {
        public Guid LeadId { get; set; }
        public AppointmentType MeetingOrSiteVisit { get; set; }
        public bool IsDone { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? ProjectName { get; set; }
        public string? ExecutiveName { get; set; }
        public string? ExecutiveContactNo { get; set; }
        public string? Image { get; set; }
        public List<LeadDocument>? ImagesWithName { get; set; }
        public bool? IsManual { get; set; }
        public string? Notes { get; set; }
        public AddressDto? Address { get; set; }
        public string? PropertyName { get; set; }
        public Guid? UserId { get; set; }
        public DateTime? AppointmentDoneOn { get; set; }
    }
    public class UpdateSiteVisitOrMeetingDoneRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateSiteVisitOrMeetingDoneRequest, Response<bool>>
    {
        public UpdateSiteVisitOrMeetingDoneRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(UpdateSiteVisitOrMeetingDoneRequestHandler).Name, "Handle")
        {
        }

        public async Task<Response<bool>> Handle(UpdateSiteVisitOrMeetingDoneRequest request, CancellationToken cancellationToken)
        {
            try
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.LeadId), cancellationToken) ?? throw new NotFoundException("No Lead exists with the given Id");

                try
                {
                    lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(lead, request.Adapt<PickedLeadDto>());
                }
                catch (Exception ex)
                {
                    throw;
                }

                var appointment = request.Adapt<LeadAppointment>();
                appointment.Location = request.Address?.Adapt<Address>();
                appointment.Latitude = request.Latitude == default ? double.TryParse(request.Address?.Latitude, out double lat) ? lat : request.Latitude : default;
                appointment.Longitude = request.Longitude == default ? double.TryParse(request.Address?.Longitude, out double lng) ? lng : request.Longitude : default;
                appointment.UniqueKey = Guid.NewGuid();

                #region appointment done and not done by user
                var currentUser = _currentUser.GetUserId();

                if (request.UserId.HasValue && request.UserId.Value != Guid.Empty)
                {
                    appointment.UserId = request.UserId.Value;
                    currentUser = request.UserId.Value;
                }
                else
                {
                    appointment.UserId = lead.AssignTo;
                }

                #endregion

                await SetLeadAppointmentAsync(lead, appointment, request.MeetingOrSiteVisit, request.ImagesWithName, cancellationToken);
                //Add Appointment Documents to Lead Documents
                if (request.ImagesWithName != null && request.ImagesWithName.Any())
                {
                    var docType = request.MeetingOrSiteVisit == AppointmentType.Meeting ? Domain.Enums.LeadDocumentType.Meeting : request.MeetingOrSiteVisit == AppointmentType.SiteVisit ? Domain.Enums.LeadDocumentType.SiteVisit : Domain.Enums.LeadDocumentType.None;
                    await SetLeadDocsAsync(lead, request.ImagesWithName, docType, cancellationToken);
                }
                lead.Notes = appointment.Notes;
                lead.AppointmentDoneOn = appointment.AppointmentDoneOn;
                await _leadRepo.UpdateAsync(lead);
                #region Facebook Conversion event trigger
                try
                {
                    var globalSetting = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
                    if (globalSetting != null && globalSetting.EnableFacebookConversion)
                    {
                        if ((!string.IsNullOrEmpty(lead.MetaLeadId)) && (!string.IsNullOrEmpty(lead.PixelId)))
                        {
                            var fbPageAccount = await _fbAuthResponseRepo.FirstOrDefaultAsync(new GetFacebookAuthResoponseAccountSpec(lead.PixelId ?? ""));
                            var eventName = "";
                            if (request.IsDone && request.MeetingOrSiteVisit != AppointmentType.None)
                            {
                                if (request.MeetingOrSiteVisit == AppointmentType.Meeting)
                                {
                                    eventName = "MeetingDone";
                                }
                                else
                                {
                                    eventName = "SiteVisitDone";
                                }
                            }
                            else if (request.IsDone && request.MeetingOrSiteVisit != AppointmentType.None)
                            {
                                if (request.MeetingOrSiteVisit == AppointmentType.Meeting)
                                {
                                    eventName = "MeetingNotDone";
                                }
                                else
                                {
                                    eventName = "SiteVisitNotDone";
                                }
                            }
                            var metaStatuses = fbPageAccount?.MetaLeadStatusMapping?
                                        .FirstOrDefault(kv => kv.Value.Contains(eventName ?? "")).Key;
                            FbConversionApiDto fbConversionApiDto = new();
                            fbConversionApiDto.AccessToken = fbPageAccount?.ConversionsAccessToken;
                            fbConversionApiDto.MetaLeadIds = new List<string> { lead.MetaLeadId ?? "" };
                            fbConversionApiDto.StatusName = eventName;
                            fbConversionApiDto.PixelId = lead.PixelId;
                            fbConversionApiDto.MetaStatus = metaStatuses ?? MetaLeadUnifiedStatus.None;
                            var payload = new InputPayload(_currentUser.GetTenant() ?? string.Empty, currentUser, string.Empty, fbConversionApiDto);
                            await _serviceBus.RunFbConversionApiEventAsync(payload);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateLeadStatusRequestHandler exception details  => () => Facebook Conversion event trigger " + ex.Serialize());
                    Log.Information("UpdateLeadStatusRequestHandler exception details  => () => Facebook Conversion event trigger" + ex.Serialize());
                }
                #endregion
                var leadDto = lead.Adapt<ViewLeadDto>();
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId: currentUser);

                await UpdateLeadHistoryAsync(lead, leadDto, appointmentType: request.MeetingOrSiteVisit, cancellationToken: cancellationToken, currentUserId: currentUser);

                await SendLeadAppointmentNotificationAsync(lead, request.MeetingOrSiteVisit, request.IsDone, globalSettings, cancellationToken);

                return new(true);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(AddDocumentRequestHandler).Name} - Handle()");
                throw;
            }
        }
    }

}
