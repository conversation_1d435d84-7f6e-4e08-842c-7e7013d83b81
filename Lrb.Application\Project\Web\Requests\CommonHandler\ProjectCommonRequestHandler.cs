﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Application.ZonewiseLocation.Web.Helpers;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using System.Runtime;

namespace Lrb.Application.Project.Web.Requests.CommonHandler
{
    public class ProjectCommonRequestHandler
    {
        private readonly IServiceProvider _serviceProvider;
        protected readonly IRepositoryWithEvents<Location> _locationRepo;
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IGooglePlacesService _googlePlacesService;
        protected readonly IRepositoryWithEvents<CustomMasterAttribute> _masterProjectUnitAttributes;
        protected readonly IRepositoryWithEvents<CustomMasterAmenity> _masterProjectAmenityRepo;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<CustomMasterProjectType> _customProjectTypeRepo;
        protected readonly IRepositoryWithEvents<UnitType> _unitTypeRepo;
        protected readonly IRepositoryWithEvents<Block> _blockRepo;
        protected readonly IProjectRepository _projectRepository;
        protected readonly IDapperRepository _dapperRepository;
        protected readonly ICurrentUser _currentUser;
        public ProjectCommonRequestHandler(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _locationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Location>>(); ;
            _mediator = _serviceProvider.GetRequiredService<IMediator>(); ;
            _addressRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Address>>();
            _googlePlacesService = _serviceProvider.GetRequiredService<IGooglePlacesService>();
            _masterProjectUnitAttributes = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterAttribute>>();
            _masterProjectAmenityRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterAmenity>>();
            _projectRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Lrb.Domain.Entities.Project>>();
            _customProjectTypeRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterProjectType>>();
            _unitTypeRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<UnitType>>();
            _blockRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Block>>();
            _projectRepository = _serviceProvider.GetRequiredService<IProjectRepository>();
            _dapperRepository = _serviceProvider.GetRequiredService<IDapperRepository>();
            _currentUser = _serviceProvider.GetRequiredService<ICurrentUser>();
        }

        protected async Task<Address?> CreateAddressAsync(AddressDto? addressDto, CancellationToken cancellationToken = default)
        {
            try
            {
                Address? address = null;
                if (addressDto?.LocationId != null && addressDto?.LocationId != Guid.Empty)
                {
                    address = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocationIdSpec(new() { addressDto?.LocationId ?? Guid.Empty }), cancellationToken);
                    if (address == null)
                    {
                        var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addressDto?.LocationId ?? Guid.Empty), cancellationToken);
                        if (location != null)
                        {
                            address = location.MapToAddress();
                            if (address != null)
                            {
                                address.Id = Guid.Empty;
                                address = await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                }
                else if (!string.IsNullOrWhiteSpace(addressDto?.PlaceId) && address == null)
                {
                    address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(addressDto.PlaceId), cancellationToken)).FirstOrDefault();
                    if (address == null)
                    {
                        try
                        {
                            address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(addressDto.PlaceId))?.Adapt<Address>() ?? null;
                        }
                        catch (Exception ex)
                        {

                        }
                        if (address != null)
                        {
                            address = await _addressRepo.AddAsync(address);
                            await MapAddressToLocationAndSaveAsync(address);
                        }
                    }
                }
                else if (double.TryParse(addressDto?.Longitude ?? "0", out double lng) && lng > 0 && double.TryParse(addressDto?.Latitude ?? "0", out double lat) && lat > 0)
                {
                    var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
                    var place = places.FirstOrDefault();
                    if (place != null && place.PlaceId != null)
                    {
                        address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                        if (address == null)
                        {
                            try
                            {
                                address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(place.PlaceId))?.Adapt<Address>() ?? null;
                            }
                            catch (Exception ex)
                            {

                            }
                            if (address != null)
                            {
                                address = await _addressRepo.AddAsync(address);
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                    }
                    else if (place != null)
                    {
                        address = place.Adapt<Address>();
                        address = await _addressRepo.AddAsync(address);
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                else if (address == null)
                {
                    if (addressDto != null)
                    {
                        var newAddress = addressDto?.Adapt<Address>();
                        if (newAddress != null)
                        {
                            var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                            if (existingAddress != null)
                            {
                                newAddress.Adapt(existingAddress);
                                address = existingAddress;
                            }
                            else
                             {
                                address = await _addressRepo.AddAsync(newAddress);
                                await MapAddressToLocationAndSaveAsync(address);
                            }

                        }
                    }
                }
                return address;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        protected async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            try
            {
                var location = address.MapToLocationRequest();
                if (location != null)
                {
                    var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                    var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                    address.Location = createdLocation;
                    await _addressRepo.UpdateAsync(address);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        protected async Task<List<ViewUnitTypeDto>> MapProjectUnitAttributes(List<ViewUnitTypeDto> viewDtos, CancellationToken cancellationToken,List<FormFieldValueDto> fieldValues=null)
        {
            var masterProjectAttributes = await _masterProjectUnitAttributes.ListAsync(cancellationToken);
            var formFieldIds = fieldValues.Select(i => i.FormFieldId).Distinct().ToList();


            if (viewDtos?.Any() ?? false)
            {
                foreach (var unit in viewDtos)
                {
                    if (unit != null && (unit.Attributes?.Any() ?? false))
                    {
                        List<UnitTypeAttributeDto> attributesDto = new();
                        foreach (var attribute in unit.Attributes)
                        {
                            var masterAttribute = masterProjectAttributes.Where(i => i.Id == attribute.MasterProjectUnitAttributeId).FirstOrDefault();
                            if (masterAttribute != null)
                            {
                                attribute.AttributeName = masterAttribute.AttributeName;
                                attributesDto.Add(attribute);
                            }
                            else
                            {
                                attributesDto.Add(attribute);
                            }
                        }
                        unit.Attributes = attributesDto;
                    }
                    if (fieldValues != null)
                    {
                        var unitValues = fieldValues.Where(i => i.EntityChildId == unit.Id).ToList();
                        var customfileds = formFieldIds.Select(fieldId =>
                        {
                            var value = unitValues.FirstOrDefault(v => v.FormFieldId == fieldId);

                            return new FormFieldValueDto
                            {
                                FormFieldId = fieldId,
                                EntityId = value?.EntityId,
                                EntityChildId = unit.Id,
                                Value = value?.Value
                            };
                        }).ToList();

                        unit.CustomFields = customfileds;
                    }
                }
            }
            return viewDtos;
        }


        protected async Task<ViewProjectDto> MapProjectUnitAttributes(ViewProjectDto viewDto, CancellationToken cancellationToken)
        {
            var masterProjectAttributes = await _masterProjectUnitAttributes.ListAsync(cancellationToken);
            if (viewDto != null && (viewDto.UnitTypes?.Any() ?? false))
            {
                foreach (var unittype in viewDto.UnitTypes)
                {
                    if (unittype != null && (unittype.Attributes?.Any() ?? false))
                    {
                        List<UnitTypeAttributeDto> attributesDto = new();
                        foreach (var attribute in unittype.Attributes)
                        {
                            var masterAttribute = masterProjectAttributes.Where(i => i.Id == attribute.MasterProjectUnitAttributeId).FirstOrDefault();
                            if (masterAttribute != null)
                            {
                                attribute.AttributeName = masterAttribute.AttributeName;
                                attributesDto.Add(attribute);
                            }
                            else
                            {
                                attributesDto.Add(attribute);
                            }
                        }
                        unittype.Attributes = attributesDto;
                    }
                }
            }
            return viewDto;
        }

        protected async Task<ProjectTopLevelCountDto> AddProjectTopLevelCount(ProjectTopLevelCountDto projectCount, GetAllProjectRequest request, System.Reflection.PropertyInfo propertyInfo, List<Guid>? projectIds = null)
        {
            switch (propertyInfo.Name)
            {
                case nameof(ProjectTopLevelCountDto.All):
                    request.ProjectVisibility = ProjectVisibilityType.All;
                    //projectCount.All = await _projectRepo.CountAsync(new ProjectByCustomFilterSpec(request));
                    projectCount.All = await _projectRepository.GetAllProjectsCountForWebNewAsync(request, projectIds);
                    break;

                case nameof(ProjectTopLevelCountDto.Residential):
                    request.ProjectVisibility = ProjectVisibilityType.Residential;
                    //projectCount.Residential = await _projectRepo.CountAsync(new ProjectByCustomFilterSpec(request));
                    projectCount.Residential = await _projectRepository.GetAllProjectsCountForWebNewAsync(request, projectIds);
                    break;

                case nameof(ProjectTopLevelCountDto.Commercial):
                    request.ProjectVisibility = ProjectVisibilityType.Commercial;
                    //projectCount.Commercial = await _projectRepo.CountAsync(new ProjectByCustomFilterSpec(request));
                    projectCount.Commercial = await _projectRepository.GetAllProjectsCountForWebNewAsync(request, projectIds);
                    break;

                case nameof(ProjectTopLevelCountDto.Agriculture):
                    request.ProjectVisibility = ProjectVisibilityType.Agriculture;
                    //projectCount.Agriculture = await _projectRepo.CountAsync(new ProjectByCustomFilterSpec(request));
                    projectCount.Agriculture = await _projectRepository.GetAllProjectsCountForWebNewAsync(request, projectIds);
                    break;

                case nameof(ProjectTopLevelCountDto.Deleted):
                    request.ProjectVisibility = ProjectVisibilityType.Deleted;
                    //projectCount.Deleted = await _projectRepo.CountAsync(new ProjectByCustomFilterSpec(request));
                    projectCount.Deleted = await _projectRepository.GetAllProjectsCountForWebNewAsync(request, projectIds);
                    break;
            }
            return projectCount;
        }
    }
}
