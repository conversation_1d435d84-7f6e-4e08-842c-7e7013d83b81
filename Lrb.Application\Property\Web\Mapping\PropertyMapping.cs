﻿using Lrb.Application.Lead.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Dtos.XMl_Feed;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using System.Threading;

namespace Lrb.Application.Property.Web
{
    public static class PropertyMapping
    {
        public static IRepository<MasterPropertyType> _masterPropertyTypeRepo = null;
        public static IRepository<MasterPropertyAttribute> _masterPropertyAttributeRepo = null;
        public static IRepository<MasterAreaUnit> _masterAreaUnitRepo = null;
        public static void configure(IServiceProvider serviceProvider)
        {
            _masterPropertyTypeRepo = (IRepository<MasterPropertyType>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyType)));
            _masterPropertyAttributeRepo = (IRepository<MasterPropertyAttribute>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyAttribute)));
            _masterAreaUnitRepo = (IRepository<MasterAreaUnit>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterAreaUnit)));
            List<MasterPropertyType>? propertytypes = null;
            List<MasterPropertyAttribute>? masterPropertyAttributes = null;
            if (_masterPropertyTypeRepo != null)
            {
                propertytypes = _masterPropertyTypeRepo.ListAsync().Result;
            }
            if (_masterPropertyAttributeRepo != null)
            {
                masterPropertyAttributes = _masterPropertyAttributeRepo.ListAsync(new GetAllMasterPropertyAttributeSpec()).Result;
            }
            List<MasterAreaUnit>? masterAreaUnit = null;
            if (_masterAreaUnitRepo != null)
            {
                masterAreaUnit =  _masterAreaUnitRepo.ListAsync(new GetAllMasterAreaUnitSpec()).Result;
            }
            TypeAdapterConfig<Domain.Entities.Property, ViewPropertyDto>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.MasterPropertyAmenityId) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.Where(i => i.GalleryType != PropertyGalleryType.Video).GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.Videos, src => src.Galleries != null && src.Galleries.Any() ? src.Galleries.Where(i => i.GalleryType == PropertyGalleryType.Video) : null)
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.Dimension.Unit, src => (src.Dimension != null && (src.Dimension.AreaUnitId != null && src.Dimension.AreaUnitId != Guid.Empty) && (masterAreaUnit != null) && masterAreaUnit.Any()) ? ((masterAreaUnit.Where(i => src.Dimension.AreaUnitId == i.Id) != null && masterAreaUnit.Where(i => src.Dimension.AreaUnitId == i.Id).Any()) ? masterAreaUnit.FirstOrDefault(i => src.Dimension.AreaUnitId == i.Id).Unit : null) : null)
                .Map(dest => dest.OwnerDetails, src => src.OwnerDetails ?? null)
                .Map(dest => dest.Address, src => src.Address ?? null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                //.Map(dest => dest.Projects, src => src.Projects != null && src.Projects.Any() ? src.Projects.Select(i => i.Name) : new List<string>());
                .Map(dest => dest.Project, src => src.Project != null ? src.Project.Name : null)
                .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null)
                .Map(dest => dest.DLDPermitNumber, src => src.PermitNumber)
                .Map(dest => dest.PropertyOwnerDetails, src => src.PropertyOwnerDetails ?? null);

            TypeAdapterConfig<Domain.Entities.Property, CreatePropertyDto>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.Id) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.PropertyTypeId, src => src.PropertyType != null ? src.PropertyType.Id : default)
                .Map(dest => dest.PlaceId, src => src.Address != null ? src.Address.PlaceId : null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                .Map(dest => dest.DLDPermitNumber, src => src.PermitNumber);
            TypeAdapterConfig<Domain.Entities.Property, UpdatePropertyDto>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.Id) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                
                .Map(dest => dest.PropertyTypeId, src => src.PropertyType != null ? src.PropertyType.Id : default)
                .Map(dest => dest.PlaceId, src => src.Address != null ? src.Address.PlaceId : null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i=>i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null)
                .Map(dest => dest.DLDPermitNumber, src => src.PermitNumber)
                .Map(dest => dest.PropertyOwnerDetails,src=>src.PropertyOwnerDetails);
            TypeAdapterConfig<CreatePropertyRequest, Domain.Entities.Property>
                .NewConfig()
                .Map(dest => dest.Galleries, src => src.ImageUrls != null && src.ImageUrls.Any() ? src.ImageUrls.GetPropertyGallery() : null)
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => new PropertyAmenity { MasterPropertyAmenityId = i }).ToList() : null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHKs, src => src.NoOfBHK)
                .Ignore(dest => dest.Project)
                .Map(dest => dest.PermitNumber, src => src.DLDPermitNumber);

            TypeAdapterConfig<CreateBasicPropertyInfoRequest, Domain.Entities.Property>
                .NewConfig()
                .Ignore(dest => dest.Project);

            TypeAdapterConfig<UpdatePropertyRequest, Domain.Entities.Property>
                    .NewConfig()
                    .Ignore(i => i.Address)
                    .Ignore(i => i.TagInfo)
                    .Ignore(i => i.PropertyOwnerDetails)
                    .Ignore(i => i.MonetaryInfo)
                    .Ignore(i => i.Dimension)
                    .Ignore(i => i.PropertyType)
                    .Ignore(i => i.Galleries)
                    .Ignore(i => i.Attributes)
                    .Ignore(i => i.Amenities)
                    .Ignore(i => i.SerialNo)
                    .Ignore(i => i.TenantContactInfo)
                    .Map(dest => dest.Galleries, src => src.ImageUrls != null && src.ImageUrls.Any() ? src.ImageUrls.GetPropertyGallery() : null)
                    .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => new PropertyAmenity { MasterPropertyAmenityId = i }).ToList() : null)
                    .Map(dest => dest.Brochures, src => src.Brochures)
                    .Map(dest => dest.NoOfBHKs, src => src.NoOfBHK)
                    .Ignore(dest => dest.Project)
                    .Map(dest => dest.PermitNumber, src => src.DLDPermitNumber);


            TypeAdapterConfig<PropertyDimensionDto, PropertyDimension>
                .NewConfig()
                .Map(dest => dest.AreaInSqMtr, src => src.ConversionFactor * src.Area)
                .Map(dest => dest.CarpetAreaInSqMtr, src => src.CarpetAreaConversionFactor * src.CarpetArea)
                .Map(dest => dest.SaleableAreaAreaInSqMtr, src => src.SaleableAreaConversionFactor * src.SaleableArea)
                .Map(dest => dest.BuildUpAreaInSqMtr, src => src.BuildUpConversionFactor * src.BuildUpArea)
                .Map(dest => dest.NetAreaInSqMtr, src => src.NetAreaConversionFactor * src.NetArea);


            TypeAdapterConfig<Lrb.Domain.Entities.Property, PropertyMicrositeDto>
                .NewConfig()
                 .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.Where(i => i.GalleryType != PropertyGalleryType.Video).GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.Videos, src => src.Galleries != null && src.Galleries.Any() ? src.Galleries.Where(i => i.GalleryType == PropertyGalleryType.Video) : null)
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.Attributes, src => src.Attributes ?? null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.Address, src => src.Address ?? null)
                .Map(dest => dest.Brochures, src => src.Brochures);

            TypeAdapterConfig<Lrb.Domain.Entities.Property, MicrositeSimilarPropertyDto>
                 .NewConfig()
                 .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.Where(i => i.GalleryType != PropertyGalleryType.Video).GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.Videos, src => src.Galleries != null && src.Galleries.Any() ? src.Galleries.Where(i => i.GalleryType == PropertyGalleryType.Video) : null)
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.Attributes, src => src.Attributes ?? null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.Address, src => src.Address ?? null)
                .Map(dest => dest.PropertyId, src => src.Id)
                .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList():null);

            /*TypeAdapterConfig<Lrb.Domain.Entities.Property, Lrb.Domain.Entities.LeadEnquiry>
                .NewConfig()
                .Map(dest => dest.NoOfBHKs, src => src.NoOfBHKs)
                .Map(dest => dest.SaleType, src =>);*/
          
            TypeAdapterConfig<PropertyExportFilter, PropertyFormettedExportFilter>.NewConfig()
              .MapWith(src => new PropertyFormettedExportFilter
              {
                  PropertySize = src.PropertySize != null ? src.PropertySize.Area.ToString() : null,
                  CarpetArea = src.PropertySize != null ? src.PropertySize.CarpetArea.ToString() : null,
                  BuildUpArea = src.PropertySize != null ? src.PropertySize.BuildUpArea.ToString() : null,
                  SaleableArea = src.PropertySize != null ? src.PropertySize.SaleableArea.ToString() : null,
                  NetArea = src.PropertySize != null ? src.PropertySize.NetArea.ToString() : null,
                  Locations = ConvertStringListToString(src.Locations),
                  Cities = ConvertStringListToString(src.Cities),
                  EnquiredFor = src.EnquiredFor.ToString(),
                  PropertyStatus = src.PropertyStatus.ToString(),
                  PropertyTypes = ConvertListGuidetostring(src.PropertyTypes),
                  PropertySubTypes = ConvertListGuidetostring(src.PropertySubTypes),
                  Amenities = ConvertListGuidetostring(src.Amenities),
                  FromPossessionDate = src.FromPossessionDate.ToString(),
                  ToPossessionDate = src.ToPossessionDate.ToString(),
                  Projects = ConvertStringListToString(src.Projects),
                  BHKTypes = ConvertListEnumtoString(src.BHKTypes),
                  SaleTypes = ConvertListEnumtoString(src.SaleTypes),
                  FurnishStatuses = ConvertListEnumtoString(src.FurnishStatuses),
                  Facing = src.Facing.ToString(),
                  States = ConvertStringListToString(src.States),
                  NoOfBathrooms = ConvertIntListToString(src.NoOfBathrooms),
                  NoOfLivingrooms = ConvertIntListToString(src.NoOfLivingrooms),
                  NoOfBedrooms = ConvertIntListToString(src.NoOfBedrooms),
                  NoOfUtilites = ConvertIntListToString(src.NoOfUtilites),
                  NoOfKitchens = ConvertIntListToString(src.NoOfKitchens),
                  NoOfBalconies = ConvertIntListToString(src.NoOfBalconies),
                  NoOfFloor = ConvertIntListToString(src.NoOfFloor),
                  UserNames = src.UserNames != null ? src.UserNames.ToString():null ,
              });

            TypeAdapterConfig<PropertyExportFilterForListingManagement, PropertyFormettedExportFilterForListingManagement>.NewConfig()
              .MapWith(src => new PropertyFormettedExportFilterForListingManagement
              {
                  PropertySize = src.PropertySize != null ? src.PropertySize.Area.ToString() : null,
                  CarpetArea = src.PropertySize != null ? src.PropertySize.CarpetArea.ToString() : null,
                  BuildUpArea = src.PropertySize != null ? src.PropertySize.BuildUpArea.ToString() : null,
                  SaleableArea = src.PropertySize != null ? src.PropertySize.SaleableArea.ToString() : null,
                  NetArea = src.PropertySize != null ? src.PropertySize.NetArea.ToString() : null,
                  Locations = ConvertStringListToString(src.Locations),
                  Cities = ConvertStringListToString(src.Cities),
                  EnquiredFor = src.EnquiredFor.ToString(),
                  PropertyStatus = src.PropertyStatus.ToString(),
                  PropertyTypes = ConvertListGuidetostring(src.PropertyTypes),
                  PropertySubTypes = ConvertListGuidetostring(src.PropertySubTypes),
                  Amenities = ConvertListGuidetostring(src.Amenities),
                  FromPossessionDate = src.FromPossessionDate.ToString(),
                  ToPossessionDate = src.ToPossessionDate.ToString(),
                  Projects = ConvertStringListToString(src.Projects),
                  BHKTypes = ConvertListEnumtoString(src.BHKTypes),
                  SaleTypes = ConvertListEnumtoString(src.SaleTypes),
                  FurnishStatuses = ConvertListEnumtoString(src.FurnishStatuses),
                  Facing = src.Facing.ToString(),
                  States = ConvertStringListToString(src.States),
                  NoOfBathrooms = ConvertIntListToString(src.NoOfBathrooms),
                  NoOfLivingrooms = ConvertIntListToString(src.NoOfLivingrooms),
                  NoOfBedrooms = ConvertIntListToString(src.NoOfBedrooms),
                  NoOfUtilites = ConvertIntListToString(src.NoOfUtilites),
                  NoOfKitchens = ConvertIntListToString(src.NoOfKitchens),
                  NoOfBalconies = ConvertIntListToString(src.NoOfBalconies),
                  NoOfFloor = ConvertIntListToString(src.NoOfFloor),
                  UserNames = src.UserNames != null ? src.UserNames.ToString() : null,
                  Communities = ConvertStringListToString(src.Communities),
                  SubCommunities = ConvertStringListToString(src.SubCommunities),
                  PropertyVisiblity = src.PropertyVisiblity.ToString(),
                  FirstLevelFilter = src.FirstLevelFilter.ToString(),
                  SecondLevelFilter = src.SecondLevelFilter.ToString(),
                  CompletionStatus = src.CompletionStatus.ToString(),
                  ListingLevel = src.ListingLevel.ToString(),
                  ListingSourceIds = ConvertListGuidetostring(src.ListingSourceIds),
                  DeveloperName = ConvertStringListToString(src.DeveloperName),

              });

            TypeAdapterConfig<Domain.Entities.Property, ViewListingManagementDto>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.MasterPropertyAmenityId) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.Where(i => i.GalleryType != PropertyGalleryType.Video).GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.Videos, src => src.Galleries != null && src.Galleries.Any() ? src.Galleries.Where(i => i.GalleryType == PropertyGalleryType.Video) : null)
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                //.Map(dest => dest.Attributes, src => src.Attributes ?? null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.Dimension.Unit, src => (src.Dimension != null && (src.Dimension.AreaUnitId != null && src.Dimension.AreaUnitId != Guid.Empty) && (masterAreaUnit != null) && masterAreaUnit.Any()) ? ((masterAreaUnit.Where(i => src.Dimension.AreaUnitId == i.Id) != null && masterAreaUnit.Where(i => src.Dimension.AreaUnitId == i.Id).Any()) ? masterAreaUnit.FirstOrDefault(i => src.Dimension.AreaUnitId == i.Id).Unit : null) : null)
                .Map(dest => dest.OwnerDetails, src => src.OwnerDetails ?? null)
                .Map(dest => dest.Address, src => src.Address ?? null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                //.Map(dest => dest.Projects, src => src.Projects != null && src.Projects.Any() ? src.Projects.Select(i => i.Name) : new List<string>());
                .Map(dest => dest.Project, src => src.Project != null ? src.Project.Name : null)
                .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null)
                .Map(dest => dest.DLDPermitNumber, src => src.PermitNumber)
                .Map(dest => dest.ListingOnBehalf,src=>src.ListingOnBehalf);

            TypeAdapterConfig<Lrb.Domain.Entities.Property, PropertyMicrositeDtoListingManagement>
                .NewConfig()
                 .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.Where(i => i.GalleryType != PropertyGalleryType.Video).GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.Videos, src => src.Galleries != null && src.Galleries.Any() ? src.Galleries.Where(i => i.GalleryType == PropertyGalleryType.Video) : null)
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.Attributes, src => src.Attributes ?? null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.Address, src => src.Address ?? null)
                .Map(dest => dest.Brochures, src => src.Brochures);

            TypeAdapterConfig<Domain.Entities.Property, PropertyUserAssignmentDto>
                .NewConfig()
                .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null);

            TypeAdapterConfig<MasterPropertyAmenitiesDto, CustomMasterAmenity>
                .NewConfig()
                .Map(dest => dest.AmenityDisplayName, src => src.AmenityDisplayName)
                .Map(dest => dest.ImageURL, src => src.ImageURL)
                .Map(dest => dest.ImageURL, src => src.FullImageURL)
                .Map(dest => dest.Category, src => src.Category);
        }
        private static string ConvertListEnumtoString(List<SaleType>? saleTypes)
        {
            if (saleTypes == null || saleTypes.Count == 0)
                return null;



            List<string> enumStringList = saleTypes.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }
        public static IList<PropertyAttributeDto> SetAttributes(List<MasterPropertyAttribute>? masterPropertyAttributes,IList<PropertyAttribute> attributes)
        {
            IList<PropertyAttributeDto> propertyAttributes =  new List<PropertyAttributeDto>();
            foreach (var attribute in attributes)
            {
                if (masterPropertyAttributes.Any(i => i.Id == attribute.MasterPropertyAttributeId))
                {
                    PropertyAttributeDto propertyAttribute = new();
                    propertyAttribute.MasterPropertyAttributeId = attribute.MasterPropertyAttributeId;
                    propertyAttribute.Value = attribute.Value;
                    propertyAttribute.AttributeDisplayName = masterPropertyAttributes.FirstOrDefault(i => i.Id == attribute.MasterPropertyAttributeId)?.AttributeDisplayName ?? default;
                    propertyAttribute.AttributeName = masterPropertyAttributes.FirstOrDefault(i => i.Id == attribute.MasterPropertyAttributeId)?.AttributeName ?? default;
                    propertyAttributes.Add(propertyAttribute);
                }
            }
            return propertyAttributes;
        }

        private static string ConvertListEnumtoString(List<BHKType>? bHKTypes)
        {
            if (bHKTypes == null || bHKTypes.Count == 0)
                return null;



            List<string> enumStringList = bHKTypes.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }



        private static string ConvertListGuidetostring(List<Guid>? propertyTypes)
        {
            if (propertyTypes == null || propertyTypes.Count == 0)
            {
                return null;
            }



            return string.Join(",", propertyTypes.Select(g => g.ToString()));
        }

        private static string ConvertListEnumtoString(List<FurnishStatus>? furnishStatuses)
        {
            if (furnishStatuses == null || furnishStatuses.Count == 0)
                return null;



            List<string> enumStringList = furnishStatuses.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }



        private static string ConvertIntListToString(List<int> intList)
        {
            if (intList == null || intList.Count == 0)
            {
                return "";
            }



            string result = string.Join(", ", intList.Select(x => x.ToString()));
            return result;
        }



        private static string ConvertStringListToString(List<string>? locations)
        {
            if (locations == null || locations.Count == 0)
                return null;
            List<string> forstringsList = locations.Select(e => e.ToString()).ToList();
            return string.Join(", ", forstringsList);
        }


        public static List<PropertyGallery>? GetPropertyGallery(this Dictionary<string, List<PropertyGalleryDto>>? imageUrls)
        {
            if (imageUrls == null) return null;
            List<PropertyGallery> galleries = new List<PropertyGallery>();
            if (imageUrls != null && imageUrls.Any())
            {
                foreach (var group in imageUrls.Where(i => i.Value.Any(i => !string.IsNullOrWhiteSpace(i.ImageFilePath))))
                {
                    List<PropertyGalleryDto> images = group.Value;
                    if (images != null && images.Any())
                    {
                        foreach (var image in images)
                        {
                            galleries.Add(new PropertyGallery()
                            {
                                ImageKey = string.IsNullOrWhiteSpace(group.Key) ? "default" : group.Key,
                                ImageFilePath = image.ImageFilePath,
                                IsCoverImage = image.IsCoverImage
                            });
                        }
                    }
                }
            }
            return galleries;
        }
    }
}
