namespace Lrb.Application.CustomForm.Web.Dtos
{
    public class CreateCustomFormValueDto : BaseCustomFormValueDto
    {
    }

    public class UpdateCustomFormValueDto : BaseCustomFormValueDto
    {
        public Guid Id { get; set; }
    }

    public class ViewCustomFormValueDto : BaseCustomFormValueDto
    {
        public Guid Id { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public ViewCustomFormDto? Fields { get; set; }
    }

    public class BaseCustomFormValueDto : IDto
    {
        public Guid FormFieldId { get; set; }
        public Guid EntityId { get; set; }
        public string? Value { get; set; }
    }
}
