﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class InitApplicationRemoveUnwantDbContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EntityName",
                schema: "LeadratBlack",
                table: "CustomFormFields");

            migrationBuilder.DropColumn(
                name: "Notes",
                schema: "LeadratBlack",
                table: "CustomFormFields");

            migrationBuilder.AlterColumn<string>(
                name: "Module",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<bool>(
                name: "IsRequired",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "boolean",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AlterColumn<int>(
                name: "FieldType",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "integer",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<string>(
                name: "FieldName",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<Guid>(
                name: "EntityId",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<Guid>(
                name: "EntityChildId",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "EntityId",
                schema: "LeadratBlack",
                table: "CustomFieldValue",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EntityChildId",
                schema: "LeadratBlack",
                table: "CustomFormFields");

            migrationBuilder.AlterColumn<string>(
                name: "Module",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsRequired",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "boolean",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "FieldType",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "FieldName",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "EntityId",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityName",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                schema: "LeadratBlack",
                table: "CustomFormFields",
                type: "text",
                nullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "EntityId",
                schema: "LeadratBlack",
                table: "CustomFieldValue",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);
        }
    }
}
