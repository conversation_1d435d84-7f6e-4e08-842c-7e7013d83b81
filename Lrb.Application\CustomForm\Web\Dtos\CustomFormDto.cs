using Lrb.Domain.Enums;

namespace Lrb.Application.CustomForm.Web.Dtos
{
    public class CreateCustomFormDto : BaseCustomFormDto
    {
    }

    public class UpdateCustomFormDto : BaseCustomFormDto
    {
        public Guid Id { get; set; }
    }

    public class ViewCustomFormDto : BaseCustomFormDto
    {
        public Guid Id { get; set; }
       
    }

    public class BaseCustomFormDto : IDto
    {
        public string? FieldDisplayName { get; set; }
        public QRFormType? FieldType { get; set; }
        public string? Module { get; set; } = default!;
        public Guid? EntityId { get; set; }
        public Guid? EntityChildId { get; set; }
    }
}
