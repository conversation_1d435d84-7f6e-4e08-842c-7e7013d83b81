using Lrb.Application.CustomForm.Web.Dtos;

namespace Lrb.Application.CustomForm.Web.Requests
{
    public class UpdateCustomFormRequest : UpdateCustomFormDto, IRequest<Response<Guid>>
    {
    }

    public class UpdateCustomFormRequestHandler : IRequestHandler<UpdateCustomFormRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;

        public UpdateCustomFormRequestHandler(IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo)
        {
            _customFormRepo = customFormRepo;
        }

        public async Task<Response<Guid>> Handle(UpdateCustomFormRequest request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(request.FieldDisplayName))
                throw new InvalidOperationException("Name is required");

            if (string.IsNullOrEmpty(request.Module))
                throw new InvalidOperationException("Module is required");

            var existingCustomForm = await _customFormRepo.GetByIdAsync(request.Id, cancellationToken);
            if (existingCustomForm == null)
                throw new NotFoundException($"CustomForm with ID {request.Id} not found");

            var updatedCustomForm = request.Adapt<Domain.Entities.CustomFormFields>();
            updatedCustomForm.Id = request.Id;
            
            await _customFormRepo.UpdateAsync(updatedCustomForm, cancellationToken);
            return new(updatedCustomForm.Id);
        }
    }
}
