﻿using BackgroundTaskExecutors.DTOs;
using Lrb.Application.Attendance.Web.Requests;
using Lrb.Application.Common.Exceptions;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Requests;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Auth;
using Lrb.Infrastructure.Persistence.Repository;
using Lrb.Shared.Extensions;
using Mapster;
using MediatR;
using Microsoft.Graph;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Authentication;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace BackgroundTaskExecutors
{
    public class FunctionEntryPoint : IFunctionEntryPoint
    {
        protected readonly INotificationSenderService _notificationSenderService;
        protected readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        protected readonly IRepositoryWithEvents<Lead> _leadRepo;
        protected readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        protected readonly IUserService _userService;
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Property> _propertyRepo;
        protected readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        public FunctionEntryPoint(INotificationSenderService notificationSenderService,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<Lead> leadRepo,
            IUserService userService,
            IMediator mediator,
            IRepositoryWithEvents<Lrb.Domain.Entities.Property> propertyRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Project> projectRepo)
        {
            _notificationSenderService = notificationSenderService;
            _leadHistoryRepo = leadHistoryRepo;
            _leadRepo = leadRepo;
            _userService = userService;
            _mediator = mediator;
            _propertyRepo = propertyRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _projectRepo = projectRepo;
        }

        #region Notifications
        public async Task ScheduleNotificationsAsync(SendNotificationDto input)
        {
            try
            {
                Console.WriteLine($"FunctionEntryPoint ScheduleNotificationsAsync Called: {input.Serialize()}");
                var lead = JsonConvert.DeserializeObject<Lead>(input.Entity.ToString());
                await _notificationSenderService.ScheduleNotificationsAsync(input.@event, lead, input.AssignTo, input.UserName, topics: input.Topics, currentUserId: input.CurrentUserId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception Details while calling ScheduleNotificationsAsync() " + ex.Message);
                throw;
            }
        }
        #endregion

        #region AssignLeadsBasedOnScenarios
        public async Task AssignLeadsBasedOnScenariosAsync(V2AssignLeadsBasedOnScenariosRequest input)
        {
            try
            {
                Console.WriteLine($"FunctionEntryPoint AssignLeadsBasedOnScenariosAsync Called: {input.Serialize()}");
                await _mediator.Send(input);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception Details while calling UpdateLeadHistoryAsync() " + ex.Message);
                throw;
            }
        }
        #endregion

        #region AttendanceSettings
        public async Task SeedAttendanceSettingAsync(AttendanceSettingObject input, Guid? currentUserId, string tenantId)
        {
            var isSuccess = await _userService.UpdateOrAddAttendanceClaimAsync(input?.IsEnabledForAllUsers, input?.UserIds, input.Claim, tenantId, CancellationToken.None);
        }
        #endregion

        #region History
        public async Task UpdateLeadHistoryAsync(DTOs.LeadHistoryDto input)
        {
            try
            {
                Console.WriteLine($"FunctionEntryPoint UpdateLeadHistoryAsync Called: {input.Serialize()}");
                var lead = JsonConvert.DeserializeObject<Lead>(input.Lead.ToString());
                Lrb.Application.Lead.Web.ViewLeadDto leadDto = null;
                try
                {
                    if (!string.IsNullOrEmpty(input.LeadDto?.ToString() ?? string.Empty))
                    {
                        leadDto = JsonConvert.DeserializeObject<Lrb.Application.Lead.Web.ViewLeadDto>(input.LeadDto.ToString());
                    }
                }
                catch (Exception ex) { }
                await UpdateLeadHistoryAsync(lead, leadDto, input.AppointmentType, CancellationToken.None, input.ShouldUpdateContactRecord, currentUserId: input.CurrentUserId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception Details while calling UpdateLeadHistoryAsync() " + ex.Message);
                throw;
            }
        }
        protected async Task<ViewLeadDto> GetFullLeadDtoAsync(Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("Lead not found by the provided id.");
                var leadDto = fullLead.Adapt<ViewLeadDto>();
                if (leadDto.Address != null)
                {
                    leadDto.Address.Id = Guid.NewGuid();
                }
                if (leadDto.ChannelPartners?.Any() ?? false)
                {
                    leadDto.ChannelPartners.ForEach(cp => cp.Id = Guid.NewGuid());
                }
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                return leadDto;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        protected async Task UpdateLeadHistoryAsync(Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null)
        {
            try
            {
                var userId = currentUserId ?? Guid.Empty;
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = userId;
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                        }
                    }
                    else
                    {
                        await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        #endregion

        #region Assign Property Finder Property To Lead
        public async Task AssignPFPropertyDetailsAsync(string tenantId, LrbAssignPfPropertyDto lrbDto)
        {
            try
            {
                if(lrbDto?.IsPropertyListingEnable == true)
                {
                    var property = await _propertyRepo.FirstOrDefaultAsync(new PropertyByRefrenceNoSpecV2(lrbDto.RefrenceNo));
                    if(property != null)
                    {
                        var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lrbDto.LeadId));
                        if (lead != null)
                        {
                            #region Update Property
                            try
                            {
                                if (lead.Properties?.Any() ?? false)
                                {
                                    lead.Properties.Add(property);
                                }
                                else
                                {
                                    lead.Properties = new List<Lrb.Domain.Entities.Property>() { property };
                                }
                            }
                            catch (Exception ex)
                            {
                            }
                            #endregion

                            #region Update Project
                            try
                            {
                                if(property.Project != null)
                                {
                                    if (lead.Projects?.Any() ?? false)
                                    {
                                        lead.Projects.Add(property.Project);
                                    }
                                    else
                                    {
                                        lead.Projects = new List<Lrb.Domain.Entities.Project>() { property.Project };
                                    }
                                }
                               
                            }
                            catch
                            {

                            }
                            #endregion

                            await _leadRepo.UpdateAsync(lead);

                            #region Update Lead Enquiry
                            var enquiry = lead.Enquiries.Where(i => i.IsPrimary).FirstOrDefault();
                            List<int> beds = new();
                            List<int> bath = new();
                            foreach(var attribute in property.Attributes)
                            {
                                if(attribute.MasterPropertyAttributeId == Guid.Parse("a2f39612-2cbd-4af3-9cb8-b5536b1e1ff8"))
                                {
                                    var isParsed = int.TryParse(attribute.Value, out var bedroomValue);
                                    if (isParsed)
                                    {
                                        beds.Add(bedroomValue);
                                        enquiry.Beds = beds;
                                    }
                                }
                                else if (attribute.MasterPropertyAttributeId == Guid.Parse("29468e40-749e-4a10-999f-12d49529a05c"))
                                {
                                    var isParsed = int.TryParse(attribute.Value, out var bathroomValue);
                                    if (isParsed)
                                    {
                                        bath.Add(bathroomValue);
                                        enquiry.Baths = bath;
                                    }
                                }
                            }

                            if(property.MonetaryInfo.ExpectedPrice != default)
                            {
                                enquiry.UpperBudget = property.MonetaryInfo.ExpectedPrice;
                                enquiry.LowerBudget = property.MonetaryInfo.ExpectedPrice;
                            }

                            enquiry.Furnished = (property.FurnishStatus != FurnishStatus.None) ? property.FurnishStatus : null;

                            if(property.Dimension != null)
                            {
                                enquiry.CarpetArea = (property.Dimension.Area != default) ? property.Dimension.Area : null;
                                enquiry.CarpetAreaInSqMtr = (property.Dimension.AreaInSqMtr != default) ? property.Dimension.AreaInSqMtr : null;
                                enquiry.CarpetAreaUnitId = (property.Dimension.AreaUnitId != Guid.Empty) ? property.Dimension.AreaUnitId : Guid.Empty;

                                enquiry.BuiltUpArea = (property.Dimension.BuildUpArea != null) ? property.Dimension.BuildUpArea : null;
                                enquiry.BuiltUpAreaInSqMtr = (property.Dimension.BuildUpAreaInSqMtr != null) ? property.Dimension.BuildUpAreaInSqMtr : null;
                                enquiry.BuiltUpAreaUnitId = (property.Dimension.BuildUpAreaId != null) ? (property.Dimension.BuildUpAreaId ?? Guid.Empty) : Guid.Empty;
                            }

                            #endregion

                            await _leadEnquiryRepo.UpdateAsync(enquiry);

                            var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lrbDto.LeadId));

                            #region Update LeadHistory
                            var leadDto = fullLead.Adapt<ViewLeadDto>();

                            await leadDto.SetUsersInViewLeadDtoAsync(_userService, default, source: enquiry.LeadSource);

                            var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);

                            var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id));

                            if (existingLeadHistory != null)
                            {
                                await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                            }
                            #endregion
                        }
                    }
                    else
                    {
                        var pfproperties = await GetAllPropertiesFromPortal(lrbDto.ApiKey, lrbDto.SecretKey, lrbDto.RefrenceNo);
                        var pfProperty = pfproperties.properties.FirstOrDefault();
                        if (pfProperty != null)
                        {
                            var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lrbDto.LeadId));
                            if (lead != null)
                            {
                                #region Update Property
                                if (!string.IsNullOrEmpty(pfProperty.languages.FirstOrDefault().title))
                                {
                                    var propertyTitle = pfProperty.languages.FirstOrDefault().title;
                                    try
                                    {
                                        var existingProperty = await _propertyRepo.FirstOrDefaultAsync(new GetPropertyByTitleSpec(propertyTitle));
                                        if (existingProperty != null)
                                        {
                                            if (lead.Properties != null)
                                            {
                                                lead.Properties.Add(existingProperty);
                                            }
                                            else
                                            {
                                                lead.Properties = new List<Lrb.Domain.Entities.Property>() { existingProperty };
                                            }
                                        }
                                        else
                                        {
                                            Lrb.Domain.Entities.Property newproperty = new() { Title = propertyTitle };
                                            property = await _propertyRepo.AddAsync(newproperty);
                                            if (lead.Properties != null)
                                            {
                                                lead.Properties.Add(newproperty);
                                            }
                                            else
                                            {
                                                lead.Properties = new List<Lrb.Domain.Entities.Property>() { newproperty };
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                    }
                                }
                                #endregion

                                #region Update Project
                                if (!string.IsNullOrEmpty(pfProperty.project_name))
                                {
                                    try
                                    {
                                        var existingProject = await _projectRepo.FirstOrDefaultAsync(new GetNewProjectsByIdV2Spec(pfProperty.project_name));
                                        if (existingProject != null)
                                        {
                                            if (lead.Properties != null)
                                            {
                                                lead.Projects.Add(existingProject);
                                            }
                                            else
                                            {
                                                lead.Projects = new List<Lrb.Domain.Entities.Project>() { existingProject };
                                            }
                                        }
                                        else
                                        {
                                            Lrb.Domain.Entities.Project project = new() { Name = pfProperty.project_name };
                                            project = await _projectRepo.AddAsync(project);
                                            if (lead.Properties != null)
                                            {
                                                lead.Projects.Add(project);
                                            }
                                            else
                                            {
                                                lead.Projects = new List<Lrb.Domain.Entities.Project>() { project };
                                            }
                                        }
                                    }
                                    catch
                                    {

                                    }
                                }
                                #endregion

                                await _leadRepo.UpdateAsync(lead);

                                #region Update Lead Enquiry
                                var enquiry = lead.Enquiries.Where(i => i.IsPrimary).FirstOrDefault();
                                List<int> beds = new();
                                List<int> bath = new();
                                if (!string.IsNullOrEmpty(pfProperty.price.value))
                                {
                                    var budget = BudgetHelper.ConvertBugetV2(pfProperty.price.value);
                                    enquiry.UpperBudget = budget;
                                    enquiry.LowerBudget = budget;
                                }
                                if (!string.IsNullOrWhiteSpace(pfProperty?.bedrooms))
                                {
                                    var isParsed = int.TryParse(pfProperty?.bedrooms, out var bedroomValue);
                                    if (isParsed)
                                    {
                                        beds.Add(bedroomValue);
                                        enquiry.Beds = beds;
                                    }
                                }
                                if (!string.IsNullOrWhiteSpace(pfProperty?.bathrooms))
                                {
                                    var isParsed = int.TryParse(pfProperty?.bathrooms, out var bathroomValue);
                                    if (isParsed)
                                    {
                                        bath.Add(bathroomValue);
                                        enquiry.Baths = bath;
                                    }
                                }
                                if (pfProperty?.furnished == "furnished")
                                {
                                    enquiry.Furnished = FurnishStatus.Furnished;
                                }
                                else if (pfProperty?.furnished == "unfurnished")
                                {
                                    enquiry.Furnished = FurnishStatus.Unfurnished;
                                }
                                else if (pfProperty?.furnished == "semi-furnished")
                                {
                                    enquiry.Furnished = FurnishStatus.Semifurnished;
                                }
                                if ((!string.IsNullOrEmpty(pfProperty?.size)) || (!string.IsNullOrEmpty(pfProperty?.built_up_area)))
                                {
                                    var masterAreaUnit = await _masterAreaUnitRepo.FirstOrDefaultAsync(new GetMasterAreaUnitByUnitSpecs());
                                    var isCarpetArea = double.TryParse(pfProperty.size, out double carpetArea);
                                    if (isCarpetArea)
                                    {
                                        enquiry.CarpetArea = carpetArea;
                                        enquiry.CarpetAreaInSqMtr = masterAreaUnit.ConversionFactor * carpetArea;
                                        enquiry.CarpetAreaUnitId = masterAreaUnit.Id;
                                    }
                                    var isBuiltUpArea = double.TryParse(pfProperty.built_up_area, out double builtUpArea);
                                    if (isBuiltUpArea)
                                    {
                                        enquiry.BuiltUpArea = builtUpArea;
                                        enquiry.BuiltUpAreaInSqMtr = masterAreaUnit.ConversionFactor * builtUpArea;
                                        enquiry.BuiltUpAreaUnitId = masterAreaUnit.Id;
                                    }
                                }
                                #endregion

                                await _leadEnquiryRepo.UpdateAsync(enquiry);

                                var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lrbDto.LeadId));

                                #region Update LeadHistory
                                var leadDto = fullLead.Adapt<ViewLeadDto>();

                                await leadDto.SetUsersInViewLeadDtoAsync(_userService, default, source: enquiry.LeadSource);

                                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);

                                var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id));

                                if (existingLeadHistory != null)
                                {
                                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                                }
                                #endregion

                            }
                        }
                    }
                }
                else
                {
                    var pfproperties = await GetAllPropertiesFromPortal(lrbDto.ApiKey, lrbDto.SecretKey, lrbDto.RefrenceNo);
                    var pfProperty = pfproperties.properties.FirstOrDefault();
                    if (pfProperty != null)
                    {
                        var lead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lrbDto.LeadId));
                        if (lead != null)
                        {
                            #region Update Property
                            if (!string.IsNullOrEmpty(pfProperty.languages.FirstOrDefault().title))
                            {
                                var propertyTitle = pfProperty.languages.FirstOrDefault().title;
                                try
                                {
                                    var existingProperty = await _propertyRepo.FirstOrDefaultAsync(new GetPropertyByTitleSpec(propertyTitle));
                                    if (existingProperty != null)
                                    {
                                        if (lead.Properties != null)
                                        {
                                            lead.Properties.Add(existingProperty);
                                        }
                                        else
                                        {
                                            lead.Properties = new List<Lrb.Domain.Entities.Property>() { existingProperty };
                                        }
                                    }
                                    else
                                    {
                                        Lrb.Domain.Entities.Property property = new() { Title = propertyTitle };
                                        property = await _propertyRepo.AddAsync(property);
                                        if (lead.Properties != null)
                                        {
                                            lead.Properties.Add(property);
                                        }
                                        else
                                        {
                                            lead.Properties = new List<Lrb.Domain.Entities.Property>() { property };
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                }
                            }
                            #endregion

                            #region Update Project
                            if (!string.IsNullOrEmpty(pfProperty.project_name))
                            {
                                try
                                {
                                    var existingProject = await _projectRepo.FirstOrDefaultAsync(new GetNewProjectsByIdV2Spec(pfProperty.project_name));
                                    if (existingProject != null)
                                    {
                                        if (lead.Properties != null)
                                        {
                                            lead.Projects.Add(existingProject);
                                        }
                                        else
                                        {
                                            lead.Projects = new List<Lrb.Domain.Entities.Project>() { existingProject };
                                        }
                                    }
                                    else
                                    {
                                        Lrb.Domain.Entities.Project project = new() { Name = pfProperty.project_name };
                                        project = await _projectRepo.AddAsync(project);
                                        if (lead.Properties != null)
                                        {
                                            lead.Projects.Add(project);
                                        }
                                        else
                                        {
                                            lead.Projects = new List<Lrb.Domain.Entities.Project>() { project };
                                        }
                                    }
                                }
                                catch
                                {

                                }
                            }
                            #endregion

                            await _leadRepo.UpdateAsync(lead);

                            #region Update Lead Enquiry
                            var enquiry = lead.Enquiries.Where(i => i.IsPrimary).FirstOrDefault();
                            List<int> beds = new();
                            List<int> bath = new();
                            if (!string.IsNullOrEmpty(pfProperty.price.value))
                            {
                                var budget = BudgetHelper.ConvertBugetV2(pfProperty.price.value);
                                enquiry.UpperBudget = budget;
                                enquiry.LowerBudget = budget;
                            }
                            if (!string.IsNullOrWhiteSpace(pfProperty?.bedrooms))
                            {
                                var isParsed = int.TryParse(pfProperty?.bedrooms, out var bedroomValue);
                                if (isParsed)
                                {
                                    beds.Add(bedroomValue);
                                    enquiry.Beds = beds;
                                }
                            }
                            if (!string.IsNullOrWhiteSpace(pfProperty?.bathrooms))
                            {
                                var isParsed = int.TryParse(pfProperty?.bathrooms, out var bathroomValue);
                                if (isParsed)
                                {
                                    bath.Add(bathroomValue);
                                    enquiry.Baths = bath;
                                }
                            }
                            if (pfProperty?.furnished == "furnished")
                            {
                                enquiry.Furnished = FurnishStatus.Furnished;
                            }
                            else if (pfProperty?.furnished == "unfurnished")
                            {
                                enquiry.Furnished = FurnishStatus.Unfurnished;
                            }
                            else if (pfProperty?.furnished == "semi-furnished")
                            {
                                enquiry.Furnished = FurnishStatus.Semifurnished;
                            }
                            if ((!string.IsNullOrEmpty(pfProperty?.size)) || (!string.IsNullOrEmpty(pfProperty?.built_up_area)))
                            {
                                var masterAreaUnit = await _masterAreaUnitRepo.FirstOrDefaultAsync(new GetMasterAreaUnitByUnitSpecs());
                                var isCarpetArea = double.TryParse(pfProperty.size, out double carpetArea);
                                if (isCarpetArea)
                                {
                                    enquiry.CarpetArea = carpetArea;
                                    enquiry.CarpetAreaInSqMtr = masterAreaUnit.ConversionFactor * carpetArea;
                                    enquiry.CarpetAreaUnitId = masterAreaUnit.Id;
                                }
                                var isBuiltUpArea = double.TryParse(pfProperty.built_up_area, out double builtUpArea);
                                if (isBuiltUpArea)
                                {
                                    enquiry.BuiltUpArea = builtUpArea;
                                    enquiry.BuiltUpAreaInSqMtr = masterAreaUnit.ConversionFactor * builtUpArea;
                                    enquiry.BuiltUpAreaUnitId = masterAreaUnit.Id;
                                }
                            }
                            #endregion

                            await _leadEnquiryRepo.UpdateAsync(enquiry);

                            var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lrbDto.LeadId));

                            #region Update LeadHistory
                            var leadDto = fullLead.Adapt<ViewLeadDto>();

                            await leadDto.SetUsersInViewLeadDtoAsync(_userService, default, source: enquiry.LeadSource);

                            var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);

                            var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id));

                            if (existingLeadHistory != null)
                            {
                                await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), default);
                            }
                            #endregion

                        }
                    }
                }
                
            }
            catch 
            {

            }
        }


        #region Fetch PF Property By Refrence Number
        private async Task<PFPropertyResponseDto?> GetAllPropertiesFromPortal(string apiKey, string secretKey, string refrenceNo)
        {
            try
            {
                var handler = new HttpClientHandler()
                {
                    AllowAutoRedirect = false, // Disable automatic redirects
                    SslProtocols = SslProtocols.Tls12 // Ensure you're using TLS 1.2
                };
                using var client = new HttpClient(handler);
                var url = $"http://api-v2.mycrm.com/properties?filters[reference]={refrenceNo}";
                var request = new HttpRequestMessage(HttpMethod.Get, url);

                var token = await PFAuthToken(apiKey, secretKey ?? string.Empty);
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
                request.Headers.Add("Accept", "application/json");
                request.Headers.Add("Connection", "keep-alive");
                var response = await client.SendAsync(request);
                if (response.StatusCode == System.Net.HttpStatusCode.MovedPermanently)
                {
                    var newUrl = response.Headers.Location;
                    var newRequest = new HttpRequestMessage(HttpMethod.Get, newUrl);
                    newRequest.Headers.Authorization = request.Headers.Authorization; // Copy authorization header
                    newRequest.Headers.Add("User-Agent", request.Headers.UserAgent.ToString());
                    newRequest.Headers.Add("Accept", "application/json");
                    newRequest.Headers.Add("Connection", "keep-alive");

                    // Send the new request
                    var finalResponse = await client.SendAsync(newRequest);
                    finalResponse.EnsureSuccessStatusCode();
                    var content = await finalResponse.Content.ReadAsStringAsync();
                    var pfProperty = JsonConvert.DeserializeObject<PFPropertyResponseDto>(content);
                    return pfProperty;
                }
                else
                {
                    response.EnsureSuccessStatusCode();
                    var content = await response.Content.ReadAsStringAsync();
                    var pfProperty = JsonConvert.DeserializeObject<PFPropertyResponseDto>(content);
                    return pfProperty;
                }
            }
            catch (Exception ex)
            {
                return new();
            }
        }
        #endregion

        #region Call Pf Auth Api
        private async Task<string> PFAuthToken(string apiKey, string secretKey)
        {
            try
            {
                string baseUrl = "https://auth.propertyfinder.com/auth/oauth/v1/token";
                var client = new HttpClient();
                var body = new
                {
                    scope = "openid",
                    grant_type = "client_credentials"
                };
                var content = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");

                var accessToken = ConvertToBase64(apiKey, secretKey);

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, baseUrl);
                requestMessage.Headers.Add("Authorization", $"Basic {accessToken}");
                requestMessage.Content = content;
                HttpResponseMessage response = new();
                try
                {
                    Console.WriteLine("Calling PF auth api");
                    response = await client.SendAsync(requestMessage);
                    Console.WriteLine("Called Pf auth api");
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var authResponse = JsonConvert.DeserializeObject<PFAuthResponseDto>(responseContent);
                    return authResponse?.access_token ?? string.Empty;
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return string.Empty;
            }
        }

        public string ConvertToBase64(string apiKey, string secretKey)
        {
            var key = $"{apiKey}:{secretKey}";
            byte[] byteArray = Encoding.UTF8.GetBytes(key);
            string base64String = Convert.ToBase64String(byteArray);
            return base64String;
        }
        #endregion

        #endregion
    }
}
