﻿using Lrb.Application.Project.Web.Requests.CommonHandler;
using Lrb.Application.Project.Web.Specs;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetProjectByIdRequest : IRequest<Response<ViewProjectDto>>
    {
        public Guid Id { get; set; }
        public GetProjectByIdRequest(Guid id)
        {
            Id = id;
        }
    }
    public class GetProjectByIdRequestHandler : ProjectCommonRequestHandler, IRequestHandler<GetProjectByIdRequest, Response<ViewProjectDto>>
    {
        public readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        public GetProjectByIdRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Project> leadRepo,
            IServiceProvider serviceProvider
            ) : base(serviceProvider)
        {
            _projectRepo = leadRepo;
        }

        public async Task<Response<ViewProjectDto>> Handle(GetProjectByIdRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.FirstOrDefaultAsync(new ProjectByIdSpec(request.Id), cancellationToken));
            if (project == null) { return new("project not found."); }
            var projectDto = project.Adapt<ViewProjectDto>();
            await MapProjectUnitAttributes(projectDto, cancellationToken);
            return new Response<ViewProjectDto>(projectDto);
        }
    }

}
