﻿using Lrb.Domain.Entities.MasterData;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class Project : AuditableEntity, IAggregateRoot
    {
        public string Name { get; set; }
        public string? Certificates { get; set; }
        public Facing Facing { get; set; }

        [Column(TypeName = "jsonb")]
        public List<string>? ReraNumber { get; set; }
        public string? Description { get; set; }
        public double? TotalFlats { get; set; }
        public double? TotalBlocks { get; set; }
        public double? TotalFloor { get; set; }
        public double? Area { get; set; }
        public DefaultIdType? AreaUnitId { get; set; }
        public ProjectBuilderDetails? BuilderDetail { get; set; }
        public MasterProjectType? ProjectType { get; set; }
        public ProjectMonetaryInfo? MonetaryInfo { get; set; }
        public ProjectStatus Status { get; set; }
        public ProjectCurrentStatus CurrentStatus { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? PossessionDate { get; set; }
        public IList<AssociatedBank>? AssociatedBanks { get; set; }
        public Address? Address { get; set; }
        public bool IsGlobal { get; set; }
        public IList<Block>? Blocks { get; set; }
        public IList<UnitType>? UnitTypes { get; set; }
        public IList<ProjectGallery>? ProjectGalleries { get; set; }

        [Column(TypeName = "jsonb")]
        public List<Brochure>? Brochures { get; set; }
        public bool IsArchived { get; set; }
        public DateTime? ArchivedOn { get; set; }
        public Guid? ArchivedBy { get; set; }
        public IList<ProjectAmenity>? Amenities { get; set; }
        public string? SerialNo { get; set; }
        public Guid AutomationId { get; set; }
        public bool IsAutomated { get; set; }
        public UserAssignment? UserAssignment { get; set; }
        public IList<Lead>? Leads { get; set; }
        public IList<Prospect>? Prospects { get; set; }
        public IList<Property>? Properties { get; set; }
        public double? MinimumPrice { get; set; }
        public double? MaximumPrice { get; set; }
        public PossesionType PossesionType { get; set; }
        public Guid? RestoredBy { get; set; }
        public DateTime? RestoredOn { get; set; }
        public List<Facing>? Facings { get; set; }

        [Column(TypeName = "jsonb")]
        public Dictionary<ContactType, int>? ContactRecords { get; set; }

        [JsonIgnore]
        public IList<LeadBookedDetail>? BookedDetails { get; set; }
        [Column(TypeName = "jsonb")]
        public IList<string>? Links { get; set; }
        public bool? IsWaterMarkEnabled { get; set; }
        public string? Notes { get; set; }
        public Guid? UserAssignmentId { get; set; }
        public List<UserAssignment>? UserAssignments { get; set; }
        public double? Length { get; set; }
        public double? Breadth { get; set; }

    }
}
