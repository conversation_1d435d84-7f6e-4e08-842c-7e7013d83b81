﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class BreakingChineDbContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomFieldValue_CustomFormFields_FormFieldId",
                schema: "LeadratBlack",
                table: "CustomFieldValue");

            migrationBuilder.DropIndex(
                name: "IX_CustomFieldValue_FormFieldId",
                schema: "LeadratBlack",
                table: "CustomFieldValue");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_CustomFieldValue_FormFieldId",
                schema: "LeadratBlack",
                table: "CustomFieldValue",
                column: "FormFieldId");

            migrationBuilder.AddForeignKey(
                name: "FK_CustomFieldValue_CustomFormFields_FormFieldId",
                schema: "LeadratBlack",
                table: "CustomFieldValue",
                column: "FormFieldId",
                principalSchema: "LeadratBlack",
                principalTable: "CustomFormFields",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
