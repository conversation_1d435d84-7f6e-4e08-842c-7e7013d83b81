﻿using Lrb.Application.Reports.Web.Dtos.SalesDashboardTracker;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Requests.SalesDashboardTracker
{
    public class GetSalesDashboardTrackerRequest : IRequest<List<SalesDashboardTrackerDto>>
    {
        public DateTime? FromDate {  get; set; }
        public DateTime? ToDate { get;set; }
        public string? TenantId { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
    }

    public class SalesDashboardTrackerRequestHandler : IRequestHandler<GetSalesDashboardTrackerRequest, List<SalesDashboardTrackerDto>>
    {
        private readonly IDapperRepository _dapperRepository;
        public SalesDashboardTrackerRequestHandler(IDapperRepository dapperRepository) 
        {
            _dapperRepository = dapperRepository;
        }

        public async Task<List<SalesDashboardTrackerDto>> Handle(GetSalesDashboardTrackerRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(request.TenantId))
                {
                    var salesDashboardReportData = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<SalesDashboardTrackerDto>("LeadratBlack", "Get_Sales_DashBoard_Tracker", new
                    {
                        from_date = request?.FromDate,
                        to_date = request?.ToDate,
                        tenant_id = request?.TenantId,
                        pagesize = request?.PageSize,
                        pagenumber = request?.PageNumber
                    }, 300)).ToList();
                    return salesDashboardReportData;
                }
                return new List<SalesDashboardTrackerDto>();
            }
            catch(Exception ex)
            {
                return new List<SalesDashboardTrackerDto>();
            }
        }
    }
}
