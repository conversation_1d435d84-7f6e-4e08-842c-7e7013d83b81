﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.CustomForm.Web.Specs;
using Lrb.Application.Project.Web.Specs;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Project.Web.Requests
{
    public class UpdateProjectBlockDetailsRequest : IRequest<Response<Guid>>
    {
        public List<UpdateBlockDto> BlockDtos { get; set; }
        public Guid ProjectId { get; set; }
    }

    public class GetBlocksWithProjectIdSpec : Specification<Block>, ISpecification<Block>
    {
        public GetBlocksWithProjectIdSpec(Guid projectId)
        {
            Query.Where(i => i.ProjectId == projectId);
        }
    }
    public class UpdateProjectBlockDetailsRequestHandler : IRequestHandler<UpdateProjectBlockDetailsRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Block> _blockRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UnitType> _unitTypeRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IGooglePlacesService _googlePlacesService;
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFieldValue> _customFormValueRepo;

        public UpdateProjectBlockDetailsRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.Block> blockRepo,
            IRepositoryWithEvents<Domain.Entities.UnitType> unitTypeRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IGooglePlacesService googlePlacesService,
              IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo,
            IRepositoryWithEvents<Domain.Entities.CustomFieldValue> customFormValueRepo
            )
        {
            _projectRepo = projectRepo;
            _blockRepo = blockRepo;
            _unitTypeRepo = unitTypeRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _addressRepo = addressRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _googlePlacesService = googlePlacesService;
            _customFormRepo = customFormRepo;
            _customFormValueRepo = customFormValueRepo;
        }
        public async Task<Response<Guid>> Handle(UpdateProjectBlockDetailsRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.ListAsync(new ProjectByIdSpec(request.ProjectId))).FirstOrDefault();
            if (project == null)
            {
                throw new NotFoundException("Project does not exist!");
            }
            List<Domain.Entities.Block> blocks = await _blockRepo.ListAsync(new GetBlocksWithProjectIdSpec(project.Id), cancellationToken);
            List<UpdateBlockDto> blockDtos = request.BlockDtos;
            foreach (UpdateBlockDto blockDto in blockDtos)
            {
                var blockToUpdate = blocks.FirstOrDefault(a => a.Id == blockDto.Id);
                if (blockToUpdate != null)
                {
                    await _blockRepo.UpdateAsync(blockDto.Adapt(blockToUpdate));
                }
                if (blockDto?.CustomFields?.Any() ?? false)
                {
                    await UpdateCustomFieldValues(request.ProjectId, blockDto.CustomFields, blockDto.Id, cancellationToken);
                }
            }
            return new(project.Id);
        }

        public async Task UpdateCustomFieldValues(Guid projectId, List<FormFieldValueDto> customFields, Guid blockId, CancellationToken cancellationToken)
        {
            List<CustomFieldValue> formFieldValues = new();
            var formFieldIds = customFields.Select(f => f.FormFieldId ?? Guid.Empty).Distinct().ToList();
            var fieldDict = (await _customFormRepo.ListAsync(new GetCustomFormFields(formFieldIds), cancellationToken)).ToDictionary(f => f.Id, f => f);
            var existingDict = (await _customFormValueRepo.ListAsync(new GetUnitsByFieldIdsSpec(projectId, formFieldIds, blockId), cancellationToken)).ToDictionary(ev => ev.FormFieldId, ev => ev);
           
            formFieldValues = customFields.Where(fv => fv.FormFieldId.HasValue && fieldDict.ContainsKey(fv.FormFieldId.Value)).Select(fv =>
            {
                var formFieldId = fv.FormFieldId.Value;
                if (existingDict.TryGetValue(formFieldId, out var existingUnit))
                {
                    existingUnit.Value = fv.Value;
                    existingUnit.IsDeleted = false;
                    return existingUnit;
                }
                else
                {
                    return new CustomFieldValue
                    {
                        FormFieldId = formFieldId,
                        EntityId = projectId,
                        Value = fv.Value,
                        IsDeleted = false,
                        EntityChildId = blockId
                    };
                }
            }).ToList();

            if (formFieldValues.Any())
            {
                await _customFormValueRepo.UpdateRangeAsync(formFieldValues, cancellationToken);
            }
        }
    }
}
