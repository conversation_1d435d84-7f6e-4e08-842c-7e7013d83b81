﻿using System.Collections.Generic;
using Lrb.Domain.Enums;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class pixelIntegrationChanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Dictionary<MetaLeadUnifiedStatus, List<string>>>(
                name: "MetaLeadStatusMapping",
                schema: "LeadratBlack",
                table: "FacebookAuthResponses",
                type: "jsonb",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MetaLeadStatusMapping",
                schema: "LeadratBlack",
                table: "FacebookAuthResponses");
        }
    }
}
