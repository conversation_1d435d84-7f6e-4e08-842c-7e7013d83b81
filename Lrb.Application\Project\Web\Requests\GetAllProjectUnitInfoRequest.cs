﻿using Amazon.DynamoDBv2;
using Lrb.Application.Project.Web.Requests.CommonHandler;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Requests;
using System.Runtime;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetAllProjectUnitInfoRequest : PaginationFilter, IRequest<PagedResponse<ViewUnitTypeDto, string>>
    {
        public Guid ProjectId { get; set; }
        public string? Search { get; set; }
        public double? Area { get; set; }
        public double? MinArea { get; set; }
        public double? MaxArea { get; set; }
        public Guid? AreaUnitId { get; set; }
        public double? CarpetArea { get; set; }
        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public Guid? CarpetAreaUnitId { get; set; }
        public double? BuiltupArea { get; set; }
        public double? MinBuiltUpArea { get; set; }
        public double? MaxBuiltUpArea { get; set; }
        public Guid? BuiltupAreaUnitId { get; set; }
        public double? SuperBuiltupArea { get; set; }
        public double? MinSuperBuiltupArea { get; set; }
        public double? MaxSuperBuiltupArea { get; set; }
        public Guid? SuperBuiltupAreaUnitId { get; set; }
        public double? MaintenanceCost { get; set; }
        public double? MinMaintenanceCost { get; set; }
        public double? MaxMaintenanceCost { get; set; }
        public double? PricePerUnit { get; set; }
        public double? MinPricePerUnit { get; set; }
        public double? MaxPricePerUnit { get; set; }
        public double? TotalPrice { get; set; }
        public double? MinTotalPrice { get; set; }
        public double? MaxTotalPrice { get; set; }
        public string? Currency { get; set; }
        public List<Guid>? UnitType { get; set; }
        public List<Guid>? UnitSubType { get; set; }
        public List<double>? BHKs { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<Facing>? Facings { get; set; }
        public List<FurnishStatus>? FurnishingStatuses { get; set; }
        public List<int>? NoOfBalconies { get; set; }
        public List<int>? NoOfBathrooms { get; set; }
        public List<int>? NoOfLivingrooms { get; set; }
        public List<int>? NoOfBedrooms { get; set; }
        public List<int>? NoOfUtilites { get; set; }
        public List<int>? NoOfKitchens { get; set; }
        public List<int>? NoOfMaximumOccupants { get; set; }
        public bool? IsWaterMarkEnabled { get; set; }
        public List<string>? NoOfFloors { get; set; }

    }

    public class GetAllProjectUnitInfoRequestHandler : ProjectCommonRequestHandler, IRequestHandler<GetAllProjectUnitInfoRequest, PagedResponse<ViewUnitTypeDto, string>>
    {

        public GetAllProjectUnitInfoRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {

        }
        public async Task<PagedResponse<ViewUnitTypeDto, string>> Handle(GetAllProjectUnitInfoRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.ListAsync(new GetProjectByIdForUnitAndBlockSpecs(request.ProjectId))).FirstOrDefault();
            if (project == null)
            {
                throw new NotFoundException("Project Not Found By Id");
            }
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();
            NumericUnitAttributesDto numericAttributeDto = InitializationOfNumericAttributes(noOfAttributes, request);
            var tenantId = _currentUser.GetTenant();

            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null || request?.NoOfMaximumOccupants != null || request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            var units = await _unitTypeRepo.ListAsync(new GetAllProjectUnitSpecs(request, numericAttributeDto, attributes), cancellationToken);
            var count = await _unitTypeRepo.CountAsync(new GetAllProjectUnitSpecs(request, numericAttributeDto, attributes), cancellationToken);
            var unitInfos = units.Adapt<List<ViewUnitTypeDto>>();

           var fieldValues= await _dapperRepository.GetFormFieldValues(tenantId ?? string.Empty, project.Id);
            unitInfos = await MapProjectUnitAttributes(unitInfos, cancellationToken, fieldValues);
            return new(unitInfos, count);
        }
        private NumericUnitAttributesDto InitializationOfNumericAttributes(List<int> noOfAttributes, GetAllProjectUnitInfoRequest request)
        {
            return new NumericUnitAttributesDto
            {
                NoOfBathrooms = FilterNumericAttributes(request.NoOfBathrooms, noOfAttributes),
                NoOfBedrooms = FilterNumericAttributes(request.NoOfBedrooms, noOfAttributes),
                NoOfKitchens = FilterNumericAttributes(request.NoOfKitchens, noOfAttributes),
                NoOfUtilites = FilterNumericAttributes(request.NoOfUtilites, noOfAttributes),
                NoOfLivingrooms = FilterNumericAttributes(request.NoOfLivingrooms, noOfAttributes),
                NoOfBalconies = FilterNumericAttributes(request.NoOfBalconies, noOfAttributes),
                NoOfMaximumOccupants = FilterNumericAttributes(request.NoOfMaximumOccupants, noOfAttributes),
                NoOfFloors = FilterNumericAttributesV1(request.NoOfFloors, noOfAttributes)

            };
        }
        private NoOfAttributeFilterDto FilterNumericAttributesV1(List<string>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();
            var selectedAttributes = new List<string>();
            if (requestValues != null)
            {
                if (requestValues.Contains("Ground Floor"))
                {
                    selectedAttributes.Add("Ground Floor");
                }
                if (requestValues.Contains("5"))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
                noOfAttributesDto.NoOfAttributes = selectedAttributes;
            }

            return noOfAttributesDto;
        }

        private NoOfAttributeFilterDto FilterNumericAttributes(List<int>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();

            if (requestValues != null)
            {
                if (requestValues.Contains(5))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
            }

            return noOfAttributesDto;
        }
        
    }
    public class NumericUnitAttributesDto
    {
        public NoOfAttributeFilterDto? NoOfBathrooms { get; set; }
        public NoOfAttributeFilterDto? NoOfBedrooms { get; set; }
        public NoOfAttributeFilterDto? NoOfKitchens { get; set; }
        public NoOfAttributeFilterDto? NoOfUtilites { get; set; }
        public NoOfAttributeFilterDto? NoOfLivingrooms { get; set; }
        public NoOfAttributeFilterDto? NoOfBalconies { get; set; }
        public NoOfAttributeFilterDto? NoOfMaximumOccupants { get; set; }
        public NoOfAttributeFilterDto? NoOfFloors { get; set; }

    }

}
