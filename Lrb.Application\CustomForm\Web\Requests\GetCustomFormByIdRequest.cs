using Lrb.Application.CustomForm.Web.Dtos;

namespace Lrb.Application.CustomForm.Web.Requests
{
    public class GetCustomFormByIdRequest : IRequest<Response<ViewCustomFormDto>>
    {
        public Guid Id { get; set; }

        public GetCustomFormByIdRequest(Guid id)
        {
            Id = id;
        }
    }

    public class GetCustomFormByIdRequestHandler : IRequestHandler<GetCustomFormByIdRequest, Response<ViewCustomFormDto>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;

        public GetCustomFormByIdRequestHandler(IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo)
        {
            _customFormRepo = customFormRepo;
        }

        public async Task<Response<ViewCustomFormDto>> Handle(GetCustomFormByIdRequest request, CancellationToken cancellationToken)
        {
            var customForm = await _customFormRepo.GetByIdAsync(request.Id, cancellationToken);
            if (customForm == null)
                throw new NotFoundException($"CustomForm with ID {request.Id} not found");

            var result = customForm.Adapt<ViewCustomFormDto>();
            return new(result);
        }
    }
}
