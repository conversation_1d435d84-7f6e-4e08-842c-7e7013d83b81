﻿
using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.PropertyRefrenceInfomation.Web.Specs;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json.Linq;
using System.Xml.Linq;

namespace Lrb.Application.ListingManagement.Web.Requests
{
    public class FetchXmlFeedListingFOrBayutAndDubizzleFromUrlRequest : IRequest<Response<bool>>
    {
        public string? Url { get; set; }
        public string? TenantId { get; set; }
        public Guid? CurrentUserId { get; set; }
    }

    public class FetchXmlFeedListingFOrBayutAndDubizzleFromUrlRequestHandler : IRequestHandler<FetchXmlFeedListingFOrBayutAndDubizzleFromUrlRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<CustomListingSource> _listingSourceRepo;
        private readonly IRepositoryWithEvents<ListingSourceAddress> _listingSourceAddessRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        protected readonly IDapperRepository _dapperRepository;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        protected readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        protected readonly ICurrentUser _currentUser;
        protected readonly IRepositoryWithEvents<Domain.Entities.PropertyAssignment> _propertyAssignmentRepository;
        protected readonly IRepositoryWithEvents<PropertyDimension> _propertyDimensionInfoRepository;
        protected readonly IRepositoryWithEvents<PropertyAmenity> _propertyAmenitiesRepository;
        protected readonly IRepositoryWithEvents<PropertyAttribute> _propertyAttributesRepository;
        protected readonly IRepositoryWithEvents<PropertyOwnerDetails> _propertyOwnerDetailsRepository;
        protected readonly IRepositoryWithEvents<PropertyMonetaryInfo> _propertyMonetaryInfoRepository;
        protected readonly IRepositoryWithEvents<PropertyGallery> _propertyGallleryRepository;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IRepositoryWithEvents<CustomMasterAmenity> _propertyAmenityListRepository;
        protected readonly IRepositoryWithEvents<CustomMasterAttribute> _propertyAttributeListRepository;
        protected readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertyTypeRepository;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        protected readonly IBlobStorageService _blobStorageService;
        protected readonly IRepositoryWithEvents<UserView> _userViewRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingRepo;
        public FetchXmlFeedListingFOrBayutAndDubizzleFromUrlRequestHandler(
            IRepositoryWithEvents<ListingSourceAddress> listingSourceAddessRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IDapperRepository dapperRepository,
            ILeadRepositoryAsync leadRepositoryAsync,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IReadRepository<PropertyDimension> propertyDimensionRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<PropertyAssignment> propertyAssignmentRepository,
            IRepositoryWithEvents<PropertyDimension> propertyDimensionInfoRepository,
            IRepositoryWithEvents<PropertyAmenity> propertyAmenitiesRepository,
            IRepositoryWithEvents<PropertyAttribute> propertyAttributesRepository,
            IRepositoryWithEvents<PropertyOwnerDetails> propertyOwnerDetailsRepository,
            IRepositoryWithEvents<PropertyMonetaryInfo> propertyMonetaryInfoRepository,
            IRepositoryWithEvents<PropertyGallery> propertyGallleryRepository,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<CustomMasterAmenity> propertyAmenityListRepository,
            IRepositoryWithEvents<CustomMasterAttribute> propertyAttributeListRepository,
            IRepositoryWithEvents<MasterPropertyType> masterPropertyTypeRepository,
            IRepositoryWithEvents<Domain.Entities.Project> projectsRepo,
            IBlobStorageService blobStorageService,
            IRepositoryWithEvents<UserView> userViewRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingRepo,
            IRepositoryWithEvents<CustomListingSource> listingSourceRepo
            )
        {
            _listingSourceAddessRepo = listingSourceAddessRepo;
            _propertyRepo = propertyRepo;
            _dapperRepository = dapperRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _propertyDimensionRepo = propertyDimensionRepo;
            _currentUser = currentUser;
            _propertyAssignmentRepository = propertyAssignmentRepository;
            _propertyDimensionInfoRepository = propertyDimensionInfoRepository;
            _propertyAmenitiesRepository = propertyAmenitiesRepository;
            _propertyAttributesRepository = propertyAttributesRepository;
            _propertyOwnerDetailsRepository = propertyOwnerDetailsRepository;
            _propertyMonetaryInfoRepository = propertyMonetaryInfoRepository;
            _propertyGallleryRepository = propertyGallleryRepository;
            _addressRepo = addressRepo;
            _propertyAmenityListRepository = propertyAmenityListRepository;
            _propertyAttributeListRepository = propertyAttributeListRepository;
            _masterPropertyTypeRepository = masterPropertyTypeRepository;
            _projectsRepo = projectsRepo;
            _blobStorageService = blobStorageService;
            _userViewRepo = userViewRepo;
            _globalSettingRepo = globalSettingRepo;
            _listingSourceRepo = listingSourceRepo;
        }

        public async Task<Response<bool>> Handle(FetchXmlFeedListingFOrBayutAndDubizzleFromUrlRequest request, CancellationToken cancellationToken)
        {
            var data = await FetchXmlListingFromUrl(request?.Url ?? string.Empty);

            var listedProperties = ConvertXmlToJson(data).property;

            if (listedProperties?.Any() ?? false)
            {
                var isAdded = await SyncPropertyAsLrbProperty(listedProperties);
                return new(isAdded);
            }
            throw new NotImplementedException();
        }

        #region Fetch Listing From Url
        public async Task<string> FetchXmlListingFromUrl(string url)
        {
            using var httpClient = new HttpClient();
            var response = await httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
        #endregion

        #region Convert Xml to Json
        public DubizzlePropertyFeedDto ConvertXmlToJson(string xml)
        {
            try
            {
                XDocument doc = XDocument.Parse(xml);

                var properties = doc.Root.Elements("property").Select(p => new BayutXMLPropertyDto
                {
                    Bathrooms = p.Element("Bathrooms")?.Value,
                    Bedrooms = p.Element("Bedrooms")?.Value,
                    Sub_Locality = p.Element("Sub_Locality")?.Value,
                    City = p.Element("City")?.Value,
                    Listing_Agent_Email = p.Element("Listing_Agent_Email")?.Value,
                    Listing_Agent_Phone = p.Element("Listing_Agent_Phone")?.Value,
                    Property_Description = p.Element("Property_Description")?.Value,
                    developer = p.Element("developer")?.Value,
                    Features = p.Element("Features") != null ? new FeaturesDto
                    {
                        Feature = p.Element("Features").Elements("Feature").Select(f => f.Value).Distinct().ToList()
                    } : null,
                    Furnished = p.Element("Furnished")?.Value,
                    Last_Updated = p.Element("Last_Updated")?.Value,
                    Listing_Agent = p.Element("Listing_Agent")?.Value,
                    Locality = p.Element("Locality")?.Value,
                    Off_plan = p.Element("Off_plan")?.Value,
                    Permit_Number = p.Element("Permit_Number")?.Value,
                    Images = p.Element("Images") != null ? new ImagesDto
                    {
                        Images = p.Element("Images").Elements("image").Select(i => i.Value).ToList()
                    } : null,
                    Portals = p.Element("Portals") != null ? new PortalsDto
                    {
                        Portal = p.Element("Portals").Elements("portal").Select(pt => pt.Value).ToList()
                    } : null,
                    Price = p.Element("Price")?.Value,
                    property_Ref_No = p.Element("property_Ref_No")?.Value,
                    Property_Size = p.Element("Property_Size")?.Value,
                    plotArea = p.Element("plotArea")?.Value,
                    Property_Size_Unit = p.Element("Property_Size_Unit")?.Value,
                    Property_Status = p.Element("Property_Status")?.Value,
                    Property_Type = p.Element("Property_Type")?.Value,
                    Property_Title = p.Element("Property_Title")?.Value,
                    Tower_Name = p.Element("Tower_Name")?.Value,
                    Property_purpose = p.Element("Property_purpose")?.Value,
                    Videos = p.Element("Videos") != null ? new VideosDto
                    {
                        video = p.Element("Videos").Elements("video").Select(v => v.Value).ToList()
                    } : null,
                    view360 = p.Element("view360")?.Value,
                    Rent_Frequency = p.Element("Rent_Frequency")?.Value,
                    offplanDetails_dldWaiver = p.Element("offplanDetails_dldWaiver")?.Value,
                    offplanDetails_saleType = p.Element("offplanDetails_saleType")?.Value
                }).ToList();

                return new DubizzlePropertyFeedDto
                {
                    property = properties
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting XML to JSON: {ex.Message}");
                return new DubizzlePropertyFeedDto();
            }
        }
        #endregion

        #region  Sync Xml Listing as Lrb Listing
        public async Task<bool> SyncPropertyAsLrbProperty(List<BayutXMLPropertyDto> listedProperties)
        {
            try
            {
                #region Fetch All Infomation
                var properties = await _propertyRepo.ListAsync(new GetAllPropertyCheckSpecs());
                var listingSources = await _listingSourceRepo.ListAsync(new GetAllListingSourceForRefrenceSpecs());
                var source = listingSources.Where(i => i.DisplayName == "Bayut").FirstOrDefault();
                var listingAddresses = await _listingSourceAddessRepo.ListAsync(new GetListingSourceAddressBySourceIdSpecs(source?.Id ?? Guid.Empty));
                var masterPropertyTypes = await _masterPropertyTypeRepository.ListAsync();
                var masterAmenities = await _propertyAmenityListRepository.ListAsync(new GetAllMasterAminitiesSpecs());
                var masterAreaUnit = await _masterAreaUnitRepo.FirstOrDefaultAsync(new GetMasterAreaUnitByUnitSpecs());
                var masterPropertyAttributes = await _propertyAttributeListRepository.ListAsync(new GetMasterPropertyAttributesSpecs());

                #endregion

                foreach (var property in listedProperties)
                {
                    var isAddredProperty = (properties.Where(i => i.Title.ToLower().Trim() == property.Property_Title.ToLower().Trim())).FirstOrDefault();
                    if(isAddredProperty == null)
                    {
                        var createProperty = await CreatePropertyAsync(property, listingSources, listingAddresses, masterPropertyTypes);
                        createProperty = await _propertyRepo.AddAsync(createProperty);

                        #region Property Amenity

                        if (property?.Features?.Feature?.Any() ?? false)
                        {
                            var amenities = property.Features?.Feature ?? new();
                            List<PropertyAmenity> lrbAmenities = new();
                            foreach (var ame in amenities)
                            {
                                CustomMasterAmenity? masterAmenity = masterAmenities.Where(i => i.AmenityDisplayName.Contains(ame)).FirstOrDefault();

                                if (masterAmenity != null)
                                {
                                    PropertyAmenity amenity = new()
                                    {
                                        PropertyId = createProperty.Id,
                                        MasterPropertyAmenityId = masterAmenity.Id,
                                    };

                                    lrbAmenities.Add(amenity);
                                }
                            }
                            if (lrbAmenities?.Any() ?? false)
                            {
                                await _propertyAmenitiesRepository.AddRangeAsync(lrbAmenities);
                            }
                        }
                        #endregion

                        #region Property Dimension
                        if ((!string.IsNullOrEmpty(property?.Property_Size)) || (!string.IsNullOrEmpty(property?.plotArea)))
                        {
                            PropertyDimension? dimension = null;
                            var areaSize = double.TryParse(property.Property_Size, out double area);
                            var carepetArea = double.TryParse(property.plotArea, out double plotSize);
                            if ((areaSize) || (carepetArea))
                            {
                                dimension = new()
                                {
                                    Area = area,
                                    AreaUnitId = masterAreaUnit?.Id ?? Guid.Empty,
                                    CarpetArea = plotSize,
                                    CarpetAreaId = masterAreaUnit?.Id ?? Guid.Empty,
                                    PropertyId = createProperty.Id,
                                };
                                await _propertyDimensionInfoRepository.AddAsync(dimension);
                            }
                        }
                        #endregion

                        #region Property Arributes
                        if (!string.IsNullOrWhiteSpace(property?.Bedrooms))
                        {
                            var attribute = (masterPropertyAttributes.Where(i => i.AttributeName == "numberOfBedrooms")).FirstOrDefault();
                            PropertyAttribute propertyArribute = new()
                            {
                                MasterPropertyAttributeId = attribute?.Id ?? Guid.Empty,
                                PropertyId = createProperty.Id,
                                Value = property?.Bedrooms
                            };
                            await _propertyAttributesRepository.AddAsync(propertyArribute);
                        }
                        if (!string.IsNullOrWhiteSpace(property?.Bathrooms))
                        {
                            var attribute = (masterPropertyAttributes.Where(i => i.AttributeName == "numberOfBathrooms")).FirstOrDefault();
                            PropertyAttribute propertyArribute = new()
                            {
                                MasterPropertyAttributeId = attribute?.Id ?? Guid.Empty,
                                PropertyId = createProperty.Id,
                                Value = property?.Bathrooms
                            };
                            await _propertyAttributesRepository.AddAsync(propertyArribute);
                        }
                        #endregion

                        #region Property Gallery
                        var imageUrls = property?.Images?.Images;
                        if (imageUrls?.Any() ?? false)
                        {
                            var keys = await UploadImageInS3(imageUrls);
                            List<PropertyGallery> propertyGalleries = new();
                            foreach (var key in keys)
                            {
                                PropertyGallery propertyGallery = new()
                                {
                                    ImageKey = "photo",
                                    ImageFilePath = key,
                                    PropertyId = createProperty.Id,
                                    IsCoverImage = false,
                                    Name = $"PF {GenerateRandomFileName()}",
                                    GalleryType = PropertyGalleryType.Image,
                                };
                                propertyGalleries.Add(propertyGallery);
                            }

                            if (propertyGalleries?.Any() ?? false)
                            {
                                await _propertyGallleryRepository.AddRangeAsync(propertyGalleries);
                            }
                        }

                        var videoUrls = property?.Videos?.video;
                        if (videoUrls?.Any() ?? false)
                        {
                            var keys = await UploadVideosInS3(videoUrls);
                            List<PropertyGallery> propertyGalleries = new();
                            foreach (var key in keys)
                            {
                                PropertyGallery propertyGallery = new()
                                {
                                    ImageKey = "video",
                                    ImageFilePath = key,
                                    PropertyId = createProperty.Id,
                                    IsCoverImage = false,
                                    Name = $"PF {GenerateRandomFileName()}",
                                    GalleryType = PropertyGalleryType.Video,
                                };
                                propertyGalleries.Add(propertyGallery);
                            }

                            if (propertyGalleries?.Any() ?? false)
                            {
                                await _propertyGallleryRepository.AddRangeAsync(propertyGalleries);
                            }
                        }
                        #endregion
                    }
                    else
                    {
                        if (property?.Portals?.Portal is { Count: > 0 } portals)
                        {
                            var sources = listingSources.Where(ls => portals.Contains(ls.DisplayName.ToLower().Trim())).ToList();

                            if (sources.Count > 0)
                            {
                                if(isAddredProperty.ListingSources?.Any() ?? false)
                                {
                                    List<CustomListingSource> newListedSource = new();
                                    var addedSource = isAddredProperty.ListingSources.FirstOrDefault();
                                    if(addedSource != null)
                                    {
                                        newListedSource.Add(addedSource);
                                    }
                                    newListedSource.AddRange(sources);

                                    isAddredProperty.ListingSources = newListedSource;
                                }
                                else
                                {
                                    isAddredProperty.ListingSources = sources;
                                }
                                
                            }
                        }

                        #region Update Source Address
                        if ((!string.IsNullOrWhiteSpace(property?.Locality)) || (!string.IsNullOrWhiteSpace(property?.City)) || (!string.IsNullOrWhiteSpace(property?.Sub_Locality)) || (!string.IsNullOrWhiteSpace(property?.Tower_Name)))
                        {
                            ListingSourceAddress listingAddress = new();
                            if (!string.IsNullOrWhiteSpace(property?.Tower_Name))
                            {
                                List<ListingSourceAddress> sourceAddresses = new();
                                foreach (var newsource in property?.Portals?.Portal ?? new())
                                {
                                    var address = listingAddresses.Where(i =>
                                    i.TowerName.ToLower().Trim() == property?.Tower_Name?.ToLower().Trim() &&
                                    i.Community.ToLower().Trim() == property?.Locality?.ToLower().Trim() &&
                                    i.SubCommunity.ToLower().Trim() == property.Sub_Locality?.ToLower().Trim() &&
                                    i.City.ToLower().Trim() == property?.City?.ToLower().Trim() && i.ListingSource.DisplayName.ToLower().Trim() == newsource.ToLower().Trim()).FirstOrDefault();

                                    if (address == null)
                                    {
                                        var newAddress = new ListingSourceAddress()
                                        {
                                            TowerName = property?.Tower_Name,
                                            SubCommunity = property?.Sub_Locality,
                                            Community = property?.Locality,
                                            City = property?.City,
                                            ListingSource = listingSources.Where(i => i.DisplayName.ToLower().Trim() == newsource).FirstOrDefault(),
                                        };

                                        newAddress = await _listingSourceAddessRepo.AddAsync(newAddress);

                                        sourceAddresses.Add(newAddress);
                                    }
                                    else
                                    {
                                        sourceAddresses.Add( address);
                                    }
                                }

                                if (sourceAddresses?.Any() ?? false)
                                {
                                    isAddredProperty.ListingSourceAddresses = sourceAddresses;
                                }

                            }
                            else
                            {
                                List<ListingSourceAddress> sourceAddresses = new();
                                foreach (var newsource in property?.Portals?.Portal ?? new())
                                {
                                    var address = listingAddresses.Where(i =>
                                    i.Community.ToLower().Trim() == property?.Locality?.ToLower().Trim() &&
                                    i.SubCommunity.ToLower().Trim() == property.Sub_Locality?.ToLower().Trim() &&
                                    i.City.ToLower().Trim() == property?.City?.ToLower().Trim() && i.ListingSource.DisplayName.ToLower().Trim() == newsource.ToLower().Trim()).FirstOrDefault();

                                    if (address == null)
                                    {
                                        var newAddress = new ListingSourceAddress()
                                        {
                                            TowerName = property?.Tower_Name,
                                            SubCommunity = property?.Sub_Locality,
                                            Community = property?.Locality,
                                            City = property?.City,
                                            ListingSource = listingSources.Where(i => i.DisplayName.ToLower().Trim() == newsource).FirstOrDefault(),
                                        };

                                        newAddress = await _listingSourceAddessRepo.AddAsync(newAddress);

                                        sourceAddresses.Add(newAddress);
                                    }
                                    else
                                    {
                                        sourceAddresses.Add(address);
                                    }
                                }
                                if(sourceAddresses?.Any() ?? false)
                                {
                                    isAddredProperty.ListingSourceAddresses = sourceAddresses;
                                }
                            }
                        }
                        #endregion

                        await _propertyRepo.UpdateAsync(isAddredProperty);
                    }

                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        #region Create Property
        public async Task<Domain.Entities.Property> CreatePropertyAsync(BayutXMLPropertyDto listedProperty, List<CustomListingSource> listingSources, List<ListingSourceAddress> listingAddresses, List<MasterPropertyType> masterPropertyTypes)
        {
            var property = new Domain.Entities.Property()
            {
                Title = listedProperty.Property_Title,
                AboutProperty = listedProperty?.Property_Description,
                Status = PropertyStatus.Active,
                ShouldVisisbleOnListing = true,
                PermitNumber = listedProperty?.Permit_Number,
                ListingStatus = ListingStatus.Approved,
                FurnishStatus = listedProperty?.Furnished == "Yes" ? FurnishStatus.Furnished : listedProperty?.Furnished == "No" ? FurnishStatus.Unfurnished : listedProperty?.Furnished == "Partly" ? FurnishStatus.Semifurnished : FurnishStatus.None,
                CompletionStatus = listedProperty?.Off_plan?.ToLower().Trim() == "yes" ? CompletionStatus.OffPlan : CompletionStatus.Completed,
                OfferingType = listedProperty?.Off_plan?.ToLower().Trim() == "yes" ? OfferingType.OffPlan : OfferingType.Ready
            };

            if (listedProperty?.Portals?.Portal is { Count: > 0 } portals)
            {
                var sources = listingSources.Where(ls => portals.Contains(ls.DisplayName.ToLower().Trim())).ToList();

                if (sources.Count > 0)
                {
                    property.ListingSources = sources;
                }
            }

            if (!string.IsNullOrEmpty(listedProperty?.view360))
            {
                property.View360Url = new List<string>() { listedProperty?.view360 ?? string.Empty };
            }

            if (!string.IsNullOrWhiteSpace(listedProperty?.Bedrooms))
            {
                if (listedProperty?.Bedrooms.ToLower().Trim() == "studio")
                {
                    property.NoOfBHKs = 0.5;
                }
                else
                {
                    var isConverted = int.TryParse(listedProperty?.Bedrooms, out var value);
                    if (isConverted)
                    {
                        property.NoOfBHKs = value;
                    }
                }
            }

            switch (listedProperty?.Property_purpose)
            {
                case "Buy":
                    property.EnquiredFor = EnquiryType.Sale;
                    break;
                case "Rent":
                    property.EnquiredFor = EnquiryType.Rent;
                    break;
                case "Sell":
                    property.EnquiredFor = EnquiryType.Sale;
                    break;
            }

            #region Monetory Info
            if (listedProperty?.Price != null)
            {
                if (listedProperty?.Price is string stringPrice)
                {
                    var isParsed = long.TryParse(stringPrice, out long price);
                    if (isParsed)
                    {
                        var monetoryInfo = new PropertyMonetaryInfo()
                        {
                            ExpectedPrice = price,
                            IsPriceVissible = false,
                            PaymentFrequency = listedProperty.Rent_Frequency == "Yealy" ? PaymentFrequency.Yearly : listedProperty.Rent_Frequency == "Monthly" ? PaymentFrequency.Monthly : listedProperty.Rent_Frequency == "Weekly" ? PaymentFrequency.Weekly : PaymentFrequency.None,
                        };

                        property.MonetaryInfo = monetoryInfo;
                    }
                }
                else if (listedProperty?.Price is JObject jObjectPrice)
                {
                    var jsonPrice = jObjectPrice["yearly"]?.ToString();
                    var isParsed = long.TryParse(jsonPrice, out long price);
                    if (isParsed)
                    {
                        var monetoryInfo = new PropertyMonetaryInfo()
                        {
                            ExpectedPrice = price,
                            IsPriceVissible = false,
                            PaymentFrequency = listedProperty.Rent_Frequency == "Yealy" ? PaymentFrequency.Yearly : listedProperty.Rent_Frequency == "Monthly" ? PaymentFrequency.Monthly : listedProperty.Rent_Frequency == "Weekly" ? PaymentFrequency.Weekly : PaymentFrequency.None,
                        };

                        property.MonetaryInfo = monetoryInfo;
                    }
                }
            }
            #endregion

            #region Address
            if (
               (!string.IsNullOrWhiteSpace(listedProperty?.Locality)) ||
               (!string.IsNullOrWhiteSpace(listedProperty?.City)) ||
               (!string.IsNullOrWhiteSpace(listedProperty?.Sub_Locality)) ||
               (!string.IsNullOrWhiteSpace(listedProperty?.Tower_Name))
              )
            {
                ListingSourceAddress listingAddress = new();
                if (!string.IsNullOrWhiteSpace(listedProperty?.Tower_Name))
                {
                    foreach(var source in listedProperty?.Portals?.Portal ?? new())
                    {
                        var address = listingAddresses.Where(i =>
                        i.TowerName.ToLower().Trim() == listedProperty?.Tower_Name?.ToLower().Trim() &&
                        i.Community.ToLower().Trim() == listedProperty?.Locality?.ToLower().Trim() &&
                        i.SubCommunity.ToLower().Trim() == listedProperty.Sub_Locality?.ToLower().Trim() &&
                        i.City.ToLower().Trim() == listedProperty?.City?.ToLower().Trim()).FirstOrDefault();

                        if (address == null)
                        {
                            var newAddress = new ListingSourceAddress()
                            {
                                TowerName = listedProperty?.Tower_Name,
                                SubCommunity = listedProperty?.Sub_Locality,
                                Community = listedProperty?.Locality,
                                City = listedProperty?.City,
                                ListingSource = listingSources.Where(i => i.DisplayName.ToLower().Trim() == source).FirstOrDefault(),
                            };

                            newAddress = await _listingSourceAddessRepo.AddAsync(newAddress);

                            property.ListingSourceAddresses = new List<ListingSourceAddress>() { newAddress };
                        }
                        else
                        {
                            property.ListingSourceAddresses = new List<ListingSourceAddress>() { address };
                        }
                    }
                    
                }
                else
                {
                    foreach (var source in listedProperty?.Portals?.Portal ?? new())
                    {
                        var address = listingAddresses.Where(i =>
                        i.Community.ToLower().Trim() == listedProperty?.Locality?.ToLower().Trim() &&
                        i.SubCommunity.ToLower().Trim() == listedProperty.Sub_Locality?.ToLower().Trim() &&
                        i.City.ToLower().Trim() == listedProperty?.City?.ToLower().Trim()).FirstOrDefault();

                        if (address == null)
                        {
                            var newAddress = new ListingSourceAddress()
                            {
                                TowerName = listedProperty?.Tower_Name,
                                SubCommunity = listedProperty?.Sub_Locality,
                                Community = listedProperty?.Locality,
                                City = listedProperty?.City,
                                ListingSource = listingSources.Where(i => i.DisplayName.ToLower().Trim() == source).FirstOrDefault(),
                            };

                            newAddress = await _listingSourceAddessRepo.AddAsync(newAddress);

                            property.ListingSourceAddresses = new List<ListingSourceAddress>() { newAddress };
                        }
                        else
                        {
                            property.ListingSourceAddresses = new List<ListingSourceAddress>() { address };
                        }
                    }
                        
                }
            }
            #endregion

            #region Property Type
            if (!string.IsNullOrWhiteSpace(listedProperty?.Property_Type))
            {
                MasterPropertyType? masterPropertyType = masterPropertyTypes.Where(i => i.DisplayName == listedProperty?.Property_Type).FirstOrDefault();

                if (masterPropertyType != null)
                {
                    property.PropertyType = masterPropertyType;
                }
            }
            #endregion

            #region User Assignment
            if (listedProperty?.Listing_Agent != null)
            {
                var lrbUser = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(listedProperty.Listing_Agent));
                List<PropertyAssignment> propertyAssignments = new();
                if (lrbUser != null)
                {
                    PropertyAssignment propertyAssigned = new();
                    propertyAssigned.AssignedTo = lrbUser.Id;
                    propertyAssigned.AssignedUser = lrbUser.FirstName + " " + lrbUser.LastName;
                    propertyAssigned.IsCurrentlyAssigned = true;
                    propertyAssignments.Add(propertyAssigned);
                    property.PropertyAssignments = propertyAssignments;
                }
            }
            #endregion

            return property;
        }
        #endregion

        #endregion

        #region Upload Property Image In S3
        public async Task<List<string>> UploadImageInS3(List<string> imageUrl)
        {
            List<string> keys = new List<string>();
            try
            {
                foreach (var url in imageUrl)
                {
                    var client = new HttpClient();
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    var response = await client.SendAsync(request);
                    response.EnsureSuccessStatusCode();
                    var data = await response.Content.ReadAsByteArrayAsync();

                    using (var stream = new MemoryStream(data))
                    {
                        var key = await _blobStorageService.UploadObjectAsync("leadrat-black", "Images/PropertyImages", GenerateRandomFileName(), stream);
                        keys.Add(key);
                    }
                }
                return keys;
            }
            catch (Exception ex)
            {
                return keys;
            }
        }

        public string GenerateRandomFileName()
        {
            string datePart = DateTime.Now.ToString("ddMMyyyy");
            Random random = new Random();
            string randomPart = random.Next(100000000, 999999999).ToString() +
                                random.Next(100000000, 999999999).ToString();

            return datePart + randomPart;
        }

        public async Task<List<string>> UploadVideosInS3(List<string> videoUrls)
        {
            List<string> keys = new List<string>();
            try
            {
                foreach (var url in videoUrls)
                {
                    var client = new HttpClient();
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    var response = await client.SendAsync(request);
                    response.EnsureSuccessStatusCode();
                    var data = await response.Content.ReadAsByteArrayAsync();

                    using (var stream = new MemoryStream(data))
                    {
                        var key = await _blobStorageService.UploadObjectAsync("qleadrat-black", "Images/PropertyImages", GenerateRandomFileName(), stream);
                        keys.Add(key);
                    }
                }
                return keys;
            }
            catch (Exception ex)
            {
                return keys;
            }
        }
        #endregion
    }

    public class DubizzlePropertyFeedDto
    {
        public List<BayutXMLPropertyDto> property { get; set; }
    }

    public class BayutXMLPropertyDto
    {
        public string? Bathrooms { get; set; }
        public string? Bedrooms { get; set; }
        public string? Sub_Locality { get; set; }
        public string? City { get; set; }
        public string? Listing_Agent_Email { get; set; }
        public string? Listing_Agent_Phone { get; set; }
        public string? Property_Description { get; set; }
        public string? developer { get; set; }
        public FeaturesDto? Features { get; set; }
        public string? Furnished { get; set; }
        public string? Last_Updated { get; set; }
        public string? Listing_Agent { get; set; }
        public string? Locality { get; set; }
        public string? Off_plan { get; set; }
        public string? Permit_Number { get; set; }
        public ImagesDto? Images { get; set; }
        public PortalsDto? Portals { get; set; }
        public object? Price { get; set; }
        public string? property_Ref_No { get; set; }
        public string? Property_Size { get; set; }
        public string? plotArea { get; set; }
        public string? Property_Size_Unit { get; set; }
        public string? Property_Status { get; set; }
        public string? Property_Type { get; set; }
        public string? Property_Title { get; set; }
        public string? Tower_Name { get; set; }
        public string? Property_purpose { get; set; }
        public VideosDto? Videos { get; set; }
        public string? view360 { get; set; }
        public string? Rent_Frequency { get; set; }
        public string? offplanDetails_dldWaiver { get; set; }
        public string? offplanDetails_saleType { get; set; }
    }

    public class FeaturesDto
    {
        public List<string>? Feature { get; set; }
    }
    public class ImagesDto
    {
        public List<string>? Images { get; set; }
    }

    public class PortalsDto
    {
        public List<string>? Portal { get; set; }
    }
    public class VideosDto
    {
        public List<string>? video { get; set; }
    }
}
