﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Mobile.Dtos;
using Lrb.Application.Integration.Mobile.Specs;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Mappings.v1;
using Lrb.Application.Lead.Mobile.Requests.UpdateStatusHandler;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Application.Project.Mobile;
using Lrb.Application.Project.Mobile.Specs;
using Lrb.Application.Property.Mobile;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using Serilog;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Reflection;

namespace Lrb.Application.Lead.Mobile.v2
{
    public class V2UpdateLeadStatusRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public Guid LeadStatusId { get; set; }
        public string? Notes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? SoldPrice { get; set; }
        public string? BookedUnderName { get; set; }
        public DateTime? BookedDate { get; set; }
        public string? Rating { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public DateTime? PostponedDate { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? PreferredLocation { get; set; }
        public AppointmentType MeetingOrSiteVisit { get; set; }
        public bool IsDone { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public List<string>? Projects { get; set; }
        public bool? IsFullyCompleted { get; set; }
        public AddressDto? Address { get; set; }
        public List<AddressDto>? Addresses { get; set; }
        public Guid? LocationId { get; set; }
        public string? SoldPriceCurrency { get; set; } = "INR";
        public double? AgreementValue { get; set; }
        public List<Guid>? ProjectIds { get; set; }
        public List<Guid>? PropertyIds { get; set; }
        public bool? IsChoosenProperty { get; set; }
        public Guid? UnitTypeId { get; set; }
        public string? Currency { get; set; }
        public bool? IsBookingCompleted { get; set; }
        public Guid? AssignTo { get; set; }
        public Guid? SecondaryUserId { get; set; }
        public bool? IsNotesUpdated { get; set; }
        public List<LeadDocument>? Documents { get; set; }
    }
    public class V2UpdateLeadStatusRequestHandler : UpdateStatusHandler, IRequestHandler<V2UpdateLeadStatusRequest, Response<bool>>
    {
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Team> _teamRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.LeadRotationTracker> _rotationTrackerRepo;
        protected readonly ILeadRotationService _leadRotationService;
        protected readonly IRepositoryWithEvents<FacebookAuthResponse> _fbAuthResponseRepo;
        private IServiceProvider _serviceProvider;
        public V2UpdateLeadStatusRequestHandler(
           
            ILeadRepositoryAsync leadRepositoryAsync,
           
            IRepositoryWithEvents<Lrb.Domain.Entities.Team> teamRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.LeadRotationTracker> rotationTrackerRepo,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<FacebookAuthResponse> fbAuthResponseRepo,
            IServiceProvider serviceProvider) : base(serviceProvider, typeof(V2UpdateLeadStatusRequestHandler).Name, "V2UpdateLeadStatusRequestHandler")
        {
            _leadRepositoryAsync = leadRepositoryAsync;
            _teamRepo = teamRepo;
            _rotationTrackerRepo = rotationTrackerRepo;
            _leadRotationService = leadRotationService;
            _fbAuthResponseRepo = fbAuthResponseRepo;
            _serviceProvider = serviceProvider;
        }
        public async Task<CustomMasterLeadStatus?> GetCustomMasterLeadStatusAsync(Guid id, CancellationToken cancellationToken)
        {
            return await _masterLeadStatusRepo.GetByIdAsync(id, cancellationToken);
        }
        public async Task<Tuple<string>> RunParallelOperations(V2UpdateLeadStatusRequest request, Domain.Entities.Lead existingLead, string tenantId, Guid baseId)
        {
            string? baseLeadStatus = null;
            var tasks = new Task[]
            {
               Task.Run(async () => baseLeadStatus = await _dapperRepository.GetStatusNameByBaseIdAsync(baseId, tenantId ?? string.Empty)),
               Task.Run(async () => existingLead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(existingLead, request.Adapt<PickedLeadDto>())),
            };
            await Task.WhenAll(tasks);
            return new Tuple<string>(baseLeadStatus ?? string.Empty);
        }

        public async Task UpdateLeadInfoAsync(V2UpdateLeadStatusRequest request, Domain.Entities.Lead existingLead, CustomMasterLeadStatus leadStatus, CancellationToken cancellationToken, DateTime? existingBookedDate, Guid? existingBookedBy, Tuple<string>? parallelResult, Guid currentUserId)
        {
            existingLead.CustomLeadStatus = leadStatus;
            if (request.PostponedDate != null && request.PostponedDate != default)
            {
                existingLead.ScheduledDate = request.PostponedDate;
            }
            else if (leadStatus != null && leadStatus.MasterLeadStatusBaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
            {
                existingLead.ScheduledDate = null;
            }
            if ((parallelResult?.Item1 != null) && (parallelResult?.Item1 == "booked" || existingLead.CustomLeadStatus?.Status == "booked" || parallelResult?.Item1 == "invoiced" || leadStatus?.ShouldUseForBooking == true || leadStatus?.ShouldUseForInvoice == true))
            {
                if (request?.BookedDate != null)
                {
                    existingLead.BookedDate = request.BookedDate;
                }
                else
                {
                    existingLead.BookedDate = DateTime.UtcNow;
                }
                await ChangeLeadStatusToBookAsyn(request, existingLead, currentUserId, cancellationToken);
            }
            else
            {
                existingLead.BookedDate = existingBookedDate;
                existingLead.BookedBy = existingBookedBy;
            }
        }
        public async Task ChangeLeadStatusToBookAsyn(V2UpdateLeadStatusRequest request, Domain.Entities.Lead existingLead, Guid currentUserId, CancellationToken cancellationToken)
        {
            var baseLeadStatus = await _masterLeadStatusRepo?.GetByIdAsync(existingLead.CustomLeadStatus.BaseId, cancellationToken);
            if (baseLeadStatus != null && baseLeadStatus?.Status == "booked" || existingLead.CustomLeadStatus?.Status == "booked" || existingLead.CustomLeadStatus?.Status == "invoiced")
            {
                existingLead.BookedBy = currentUserId;
                var userName = await _userService.GetAsync(existingLead.BookedBy.ToString() ?? string.Empty, cancellationToken);
                var bookedDetailInfo = await _leadBookedDetailRepo.FirstOrDefaultAsync(new GetBookedDetailsByIdSpec(existingLead.Id), cancellationToken);
                if (bookedDetailInfo != null)
                {
                    try
                    {
                        bookedDetailInfo.BookedDate = existingLead?.BookedDate;
                        bookedDetailInfo.BookedBy = existingLead?.BookedBy;
                        if (userName != null)
                        {
                            bookedDetailInfo.BookedByUser = userName.FirstName + " " + userName.LastName;
                        }
                        bookedDetailInfo.BookedUnderName = existingLead?.BookedUnderName;
                        bookedDetailInfo.UserId = existingLead?.AssignTo ?? Guid.Empty;
                        bookedDetailInfo.SoldPrice = request?.SoldPrice;
                        bookedDetailInfo.Notes = request?.Notes;
                        bookedDetailInfo.ProjectsList = request?.ProjectsList;
                        bookedDetailInfo.PropertiesList = request?.PropertiesList;
                        bookedDetailInfo.AgreementValue = request?.AgreementValue ?? default;
                        bookedDetailInfo.Properties = await _propertyRepository.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                        bookedDetailInfo.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                        bookedDetailInfo.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                        bookedDetailInfo.Currency = request?.Currency ?? default;
                        bookedDetailInfo.UnitType = await _unitType.FirstOrDefaultAsync(new GetUnitInfoSpecs(request?.UnitTypeId ?? Guid.Empty)) ?? null;
                        bookedDetailInfo.IsBookingCompleted = request?.IsBookingCompleted ?? bookedDetailInfo.IsBookingCompleted;
                        await _leadBookedDetailRepo.UpdateAsync(bookedDetailInfo);
                        if (request?.AssignTo != null)
                        {
                            existingLead.AssignTo = request?.AssignTo ?? existingLead.AssignTo;
                        }
                        if (request?.SecondaryUserId != null)
                        {
                            existingLead.SecondaryUserId = request?.SecondaryUserId ?? existingLead.SecondaryUserId;
                        }
                        //await _leadRepo.UpdateAsync(existingLead);
                    }
                    catch (Exception ex)
                    {
                        await AddLrbErrorAsync(ex, $"{typeof(V2UpdateLeadStatusRequest).Name} - {typeof(V2UpdateLeadStatusRequestHandler).Name}()");
                        throw;
                    }

                }
                else
                {
                    try
                    {
                        LeadBookedDetail bookedDetail = new();
                        bookedDetail.LeadId = existingLead.Id;
                        bookedDetail.BookedDate = existingLead?.BookedDate;
                        bookedDetail.BookedBy = existingLead?.BookedBy;
                        if (userName != null)
                        {
                            bookedDetail.BookedByUser = userName?.FirstName + " " + userName?.LastName;
                        }
                        bookedDetail.BookedUnderName = existingLead?.BookedUnderName;
                        bookedDetail.UserId = existingLead?.AssignTo ?? Guid.Empty;
                        bookedDetail.SoldPrice = request?.SoldPrice;
                        bookedDetail.Notes = request?.Notes;
                        bookedDetail.ProjectsList = request?.ProjectsList;
                        bookedDetail.PropertiesList = request?.PropertiesList;
                        bookedDetail.AgreementValue = request?.AgreementValue ?? default;
                        bookedDetail.Properties = await _propertyRepository.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                        bookedDetail.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                        bookedDetail.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                        bookedDetail.Currency = request?.Currency ?? default;
                        bookedDetail.UnitType = await _unitType.FirstOrDefaultAsync(new GetUnitInfoSpecs(request?.UnitTypeId ?? Guid.Empty)) ?? null;
                        bookedDetail.IsBookingCompleted = request?.IsBookingCompleted ?? false;
                        existingLead?.BookedDetails?.Add(bookedDetail);
                        await _leadBookedDetailRepo.AddAsync(bookedDetail);
                        if (request?.AssignTo != null)
                        {
                            existingLead.AssignTo = request?.AssignTo ?? existingLead.AssignTo;
                        }
                        if (request?.SecondaryUserId != null)
                        {
                            existingLead.SecondaryUserId = request?.SecondaryUserId ?? existingLead.SecondaryUserId;
                        }
                        //  await _leadRepo.UpdateAsync(existingLead);
                    }
                    catch (Exception ex)
                    {
                        await AddLrbErrorAsync(ex, $"{typeof(V2UpdateLeadStatusRequest).Name} - {typeof(V2UpdateLeadStatusRequestHandler).Name}()");
                        throw;
                    }
                }
            }
        }
        public async Task<Response<bool>> Handle(V2UpdateLeadStatusRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var existingLead = (await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken));
                if (existingLead == null)
                {
                    throw new NotFoundException("No Lead found by this Id");
                }
                var leadStatus = await GetCustomMasterLeadStatusAsync(request.LeadStatusId, cancellationToken);
                if (leadStatus == null)
                {
                    throw new ArgumentException("The Status Id is not valid");
                }
                var tenantId = _currentUserRepo.GetTenant();
                var currentUserId = _currentUserRepo.GetUserId();
                var existingBookedDate = existingLead.BookedDate;
                var existingBookedBy = existingLead.BookedBy;
                var existingUnmatchedBudget = existingLead.UnmatchedBudget;
                var existingPurchasedFrom = existingLead.PurchasedFrom;
                var globalSetting = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
                if ((globalSetting != null) && globalSetting.IsTeamLeadRotationEnabled)
                {
                    var newAssignTo = await GetAutoRetentionAssignmentId(existingLead, request.LeadStatusId);

                    if (newAssignTo != Guid.Empty)
                    {
                        request.AssignTo = newAssignTo;
                    }
                    if ((request?.SecondaryUserId == request?.AssignTo) || (request?.SecondaryUserId == existingLead?.AssignTo))
                    {
                        request.SecondaryUserId = existingLead?.SecondaryUserId ?? Guid.Empty;
                    }
                }

                if (request?.AssignTo == null)
                {
                    request.AssignTo = existingLead?.AssignTo ?? Guid.Empty;
                }
                if (request?.AssignTo != existingLead?.AssignTo)
                {
                    existingLead.AssignedFrom = existingLead?.AssignTo ?? Guid.Empty;
                }
                if ((request?.SecondaryUserId == request?.AssignTo) || (request?.SecondaryUserId == existingLead?.AssignTo))
                {
                    request.SecondaryUserId = existingLead?.SecondaryUserId ?? Guid.Empty;
                }
                var parallelResult = await RunParallelOperations(request, existingLead, tenantId ?? string.Empty, leadStatus.BaseId ?? Guid.Empty);
                var previousnotes = existingLead.Notes;
                try
                {
                    if (request?.Documents != null && request.Documents.Any())
                    {
                        List<LeadDocument> documents = new();
                        foreach (var document in request.Documents)
                        {
                            document.Id = Guid.NewGuid();
                            document.DocumentName = $"{document.DocumentName} ({DateTime.UtcNow})";
                            document.UploadedOn = DateTime.UtcNow;
                            document.CreatedBy = currentUserId;
                            document.LastModifiedBy = currentUserId;
                            document.CreatedOn = DateTime.UtcNow;
                            document.LastModifiedOn = DateTime.UtcNow;
                            document.LeadDocumentType = Domain.Enums.LeadDocumentType.Lead;
                            documents.Add(document);
                        }
                        if (existingLead.Documents != null)
                        {
                            existingLead.Documents.AddRange(documents);
                        }
                        else
                        {
                            existingLead.Documents = documents;
                        }
                        
                    }
                    request.Documents = existingLead.Documents;
                }
                catch (Exception ex)
                {
                    //ignor
                }
                existingLead = request.Adapt(existingLead);
                try
                {
                    if (string.IsNullOrWhiteSpace(request.Notes) || (request?.UnmatchedBudget != null && request?.UnmatchedBudget != 0) || !string.IsNullOrWhiteSpace(request?.PurchasedFrom))
                    {
                        existingLead.Notes = (request?.UnmatchedBudget != null && request?.UnmatchedBudget != 0 && request?.UnmatchedBudget != existingUnmatchedBudget)
                            ? $"Unmatched Budget : {request?.UnmatchedBudget}  \n {(request?.IsNotesUpdated == true ? (request?.Notes ?? string.Empty) : string.Empty)}"
                            : (!string.IsNullOrWhiteSpace(request?.PurchasedFrom) && request?.PurchasedFrom != existingPurchasedFrom)
                                ? $"Purchased From : {request?.PurchasedFrom} \n {(request?.IsNotesUpdated == true ? (request?.Notes ?? string.Empty) : string.Empty)}"
                                : string.IsNullOrWhiteSpace(request.Notes)
                                    ? previousnotes
                                    : request.Notes;
                    }


                }
                catch (Exception ex)
                {
                    //ignore
                }

                await UpdateLeadInfoAsync(request, existingLead, leadStatus, cancellationToken, existingBookedDate, existingBookedBy, parallelResult, currentUserId);
                await _leadRepo.UpdateAsync(existingLead);
                var lead = (await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken));
                var leadDto = existingLead?.Adapt<ViewLeadDto>();
                await V2UpdateLeadStatusAsync(request, currentUserId, lead, leadDto, globalSetting, cancellationToken , tenantId);
                var newRequest = request.Adapt<UpdateStatusChangeDto>();
                newRequest.Lead = lead;
                newRequest.LeadDto = leadDto;
                newRequest.GlobalSettings = globalSetting;
                newRequest.CurrentUserId = currentUserId;
                newRequest.TenantId = tenantId;
                await Task.WhenAll(
                    CallServiceBusAsync(newRequest, tenantId ?? string.Empty, currentUserId)
                );
                //Dreamyard And Kroft Auto Lead Reassignment
                if (tenantId == "dreamyard" || tenantId == "kroft" || tenantId == "custom")  
                {
                    await HandleLeadAutoReassignment(existingLead, cancellationToken); 
                }      
                if ((globalSetting != null) && globalSetting.IsTeamLeadRotationEnabled)
                {
                    await ScheduleLeadRetentionRotation(existingLead.Id, cancellationToken);
                }

                #region Facebook Conversion event trigger
                try
                {
                    if (globalSetting != null && globalSetting.EnableFacebookConversion)
                    {
                        if ((!string.IsNullOrEmpty(existingLead.MetaLeadId)) && (!string.IsNullOrEmpty(existingLead.PixelId)))
                        {
                            var fbPageAccount = await _fbAuthResponseRepo.FirstOrDefaultAsync(new GetFacebookAuthResoponseAccountSpec(existingLead.PixelId ?? ""));
                            var metaStatuses = fbPageAccount?.MetaLeadStatusMapping?
                                        .FirstOrDefault(kv => kv.Value.Contains(leadStatus?.DisplayName ?? "")).Key;
                            FbConversionApiDto fbConversionApiDto = new();
                            fbConversionApiDto.AccessToken = fbPageAccount?.ConversionsAccessToken;
                            fbConversionApiDto.MetaLeadIds = new List<string> { existingLead.MetaLeadId ?? "" };
                            fbConversionApiDto.StatusName = leadStatus?.Status ?? "new";
                            fbConversionApiDto.PixelId = existingLead.PixelId;
                            fbConversionApiDto.MetaStatus = metaStatuses ?? MetaLeadUnifiedStatus.None;
                            var payload = new InputPayload(tenantId ?? string.Empty, currentUserId, string.Empty, fbConversionApiDto);
                            await _serviceBus.RunFbConversionApiEventAsync(payload);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateLeadStatusRequestHandler exception details  => () => Facebook Conversion event trigger " + ex.Serialize());
                    Log.Information("UpdateLeadStatusRequestHandler exception details  => () => Facebook Conversion event trigger" + ex.Serialize());
                }
                #endregion

            }
            catch (Exception ex)
            {
                Console.WriteLine("UpdateLeadStatusRequestHandler exception details  => " + ex.Serialize());
                Log.Information("UpdateLeadStatusRequestHandler exception details  => " + ex.Serialize());
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = ex?.InnerException?.Serialize() ?? default,
                    ErrorModule = "UpdateLeadStatusRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            return new(true);
        }

        private async Task CallServiceBusAsync(UpdateStatusChangeDto request, string tenantId, Guid currentUserId)
        {
            var data = request.Serialize();
            var payload = new InputPayload(tenantId, currentUserId, "statuschangenotification", data);
            await _serviceBus.RunStatusChangeNotificationJobAsync(payload);
        }
        protected async Task<bool> ShouldUpdatePickedDate(Domain.Entities.Lead existingLead, PickedLeadDto? pickedLeadDto, bool? isLeadFormUpdated = null, Guid? currentUserId = null)
        {
            if (pickedLeadDto?.AssignTo != null && pickedLeadDto.AssignTo != Guid.Empty && existingLead.AssignTo != pickedLeadDto.AssignTo)
            {
                existingLead.PickedDate = null;
                existingLead.IsPicked = false;
                return false;
            }
            bool? shouldUpdatePickedDate = null;
            currentUserId = currentUserId ?? _currentUserRepo.GetUserId();
            var originalLead = existingLead.CreateDeepCopy();
            if (originalLead != null &&
                pickedLeadDto != null &&
                (currentUserId != default && currentUserId != Guid.Empty) &&
                originalLead.AssignTo != default &&
                ((originalLead.AssignTo == currentUserId || originalLead.SecondaryUserId == currentUserId)) &&
                originalLead.PickedDate == null)
            {
                shouldUpdatePickedDate = originalLead.GetPickedDateAsync(pickedLeadDto, isLeadFormUpdated: isLeadFormUpdated).Result;
            }
            return shouldUpdatePickedDate ?? false;
        }

        // Dreamyard and kroft are the only tenants that currently use this feature
        protected async Task HandleLeadAutoReassignment(Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            var tenant = _currentUserRepo.GetTenant();
            List<Guid> leadIds = new() { lead.Id };

            if (tenant == "dreamyard" || tenant == "kroft" || tenant == "custom")    
            {
                var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamByLeadStatusId(lead?.CustomLeadStatus?.Id ?? Guid.Empty), cancellationToken);
                if (team != null)
                {
                    var tracker = await _rotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerSpecs(lead?.Id ?? Guid.Empty), cancellationToken);
                    if (tracker == null)
                    {
                        #region Add Lead Rotation Tracker
                        var newTracker = new LeadRotationTracker();
                        newTracker.LeadId = lead?.Id;
                        newTracker.NoOfRotation = team.NoOfReassignment;
                        await _rotationTrackerRepo.AddAsync(newTracker);
                        #endregion
                    }

                    AutoReassignmentForDreamyardandKroft entity = new()
                    {
                        LeadIds = leadIds
                    };
                    InputPayloadV2 payload = new(tenant, entity);
                    //var messageBody = JsonConvert.SerializeObject(payload, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
                    await _serviceBus.RunLeadRotationHttpTriggerJobAsync(payload);

                }
            }
        }

        #region Lead Retention
        public async Task ScheduleLeadRetentionRotation(Guid leadId, CancellationToken cancellationToken)
        {

            var leads = await _leadRepo.ListAsync(new LeadByIdSpec(leadId));
            await _leadRotationService.ScheduleTeamRetentionRotation(leads);
        }

        public async Task<Guid> GetAutoRetentionAssignmentId(Domain.Entities.Lead lead, Guid statusId)
        {
            Guid newAssignmentId = Guid.Empty;
            var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamByLeadStatusIdSpecs(statusId));
            var currentWeek = DateTime.UtcNow.DayOfWeek;
            if (team != null && (team?.Configurations?.FirstOrDefault(i => i.IsForRetention == true)?.DayOfWeeks?.Contains(currentWeek) ?? false) && (team?.UserIds?.Any() ?? false))
            {
                List<string>? groupUserIds = (team.UserIds.Select(i => i.ToString())).ToList();

                var userIds = await _userService.GetListOfUsersByIdsAsync(groupUserIds, default);

                var activeUser = userIds.Where(i => i.IsActive).Select(i => i.Id).ToList();

                var tracker = await _rotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerSpecs(lead.Id));

                if (tracker != null && (tracker.AssignedUsers?.Any() ?? false))
                {
                    var firstAssignedUser = tracker.AssignedUsers?.OrderBy(i => i.Key).Select(i => i.Value).FirstOrDefault();

                    var notAssignedUser = activeUser.IndexOf(firstAssignedUser ?? Guid.Empty);
                    if (notAssignedUser < (activeUser.Count - 1))
                    {
                        var assignTo = activeUser[notAssignedUser + 1];
                        newAssignmentId = assignTo;
                    }
                    else
                    {
                        var assignTo = activeUser[0];
                        newAssignmentId = assignTo;
                    }
                }
                else
                {
                    var assignTo = activeUser.FirstOrDefault();
                    newAssignmentId = assignTo;
                }
                team.Configurations.FirstOrDefault(i => i.IsForRetention == true).LastAssignedUser = newAssignmentId;
                try
                {
                    await _teamRepo.UpdateAsync(team);
                }
                catch(Exception ex) { }
            }
            return newAssignmentId;
        }
        #endregion

    }

    public record InputPayloadV2(string TenantId, object Entity);

    public class AutoReassignmentForDreamyardandKroft
    {
        public List<Guid>? LeadIds { get; set; }

    }
    public class UpdateStatusChangeDto
    {
        public Domain.Entities.GlobalSettings? GlobalSettings { get; set; }
        public Domain.Entities.Lead? Lead { get; set; }
        public ViewLeadDto? LeadDto { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }
        public Guid Id { get; set; }
        public Guid LeadStatusId { get; set; }
        public string? Notes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? SoldPrice { get; set; }
        public string? BookedUnderName { get; set; }
        public DateTime? BookedDate { get; set; }
        public string? Rating { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public DateTime? PostponedDate { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? PreferredLocation { get; set; }
        public AppointmentType MeetingOrSiteVisit { get; set; }
        public bool IsDone { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public List<string>? Projects { get; set; }
        public bool? IsFullyCompleted { get; set; }
        public AddressDto? Address { get; set; }
        public List<AddressDto>? Addresses { get; set; }
        public Guid? LocationId { get; set; }
        public string? SoldPriceCurrency { get; set; } = "INR";
        public double? AgreementValue { get; set; }
        public List<Guid>? ProjectIds { get; set; }
        public List<Guid>? PropertyIds { get; set; }
        public bool? IsChoosenProperty { get; set; }
        public Guid? UnitTypeId { get; set; }
        public string? Currency { get; set; }
        public bool? IsBookingCompleted { get; set; }
        public Guid? AssignTo { get; set; }
        public Guid? SecondaryUserId { get; set; }
        public bool? IsNotesUpdated { get; set; }
    }
}
