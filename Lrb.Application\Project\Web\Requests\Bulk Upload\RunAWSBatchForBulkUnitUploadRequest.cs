﻿using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Domain.Entities;
using Newtonsoft.Json;

namespace Lrb.Application.Project.Web.Requests.Bulk_Upload
{
    public class RunAWSBatchForBulkUnitUploadRequest :IRequest<Response<BulkUnitUploadTracker>>
    {
        public string? S3BucketKey { get; set; }
        public string? SheetName { get; set; }
        public Dictionary<UnitDataColumn, string>? MappedColumnData { get; set; }
        public List<string>? UserIds { get; set; }
        public Guid ProjectId { get; set; }
        public string? FileName { get; set; }
        public Dictionary<string, string>? CustomMappedData { get; set; }

    }

    public class RunAWSBatchForBulkUnitUploadRequestHandler : IRequestHandler<RunAWSBatchForBulkUnitUploadRequest, Response<BulkUnitUploadTracker>>
    {
        private readonly IAWSBatchService _aWSBatchService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.BulkUnitUploadTracker> _bulkUnitUploadTracker;
        public const string Type = "unit";
        private readonly IServiceBus _serviceBus;
        public RunAWSBatchForBulkUnitUploadRequestHandler(
            IAWSBatchService aWSBatchService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<Lrb.Domain.Entities.BulkUnitUploadTracker> bulkUnitUploadTracker, IServiceBus serviceBus)
        {
            _aWSBatchService = aWSBatchService;
            _currentUser = currentUser;
            _bulkUnitUploadTracker=bulkUnitUploadTracker;
            _serviceBus = serviceBus;
        }
        public async Task<Response<BulkUnitUploadTracker>> Handle(RunAWSBatchForBulkUnitUploadRequest request, CancellationToken cancellationToken)
        {
            try
            {
                BulkUnitUploadTracker bulkUnitTracker = request.Adapt<BulkUnitUploadTracker>();
                bulkUnitTracker.MappedColumnData = request.MappedColumnData;
                bulkUnitTracker.CustomMappedData = request.CustomMappedData;
                bulkUnitTracker.S3BucketKey = request.S3BucketKey;
                bulkUnitTracker.SheetName = !string.IsNullOrWhiteSpace(request.FileName) ? request.FileName + "/" + request.SheetName :
                request.S3BucketKey?.Split('/').Last() + "/" + request.SheetName;
                bulkUnitTracker.Status = UploadStatus.Initiated;
                bulkUnitTracker.ProjectIds = new List<Guid>() { request.ProjectId };
                await _bulkUnitUploadTracker.AddAsync(bulkUnitTracker);
                var tenantId = _currentUser.GetTenant();
                var userId = _currentUser.GetUserId();

                InputPayload inputPayload = new(bulkUnitTracker.Id, tenantId, userId, Type);
                var stringArgument = JsonConvert.SerializeObject(inputPayload);
                var cmdArgs = new List<string>() { stringArgument };
                await _serviceBus.RunExcelUploadJobAsync(cmdArgs);
                return new(bulkUnitTracker, " Started Processing. ");

            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }


        }
        public record InputPayload(Guid TrackerId, string TenantId, Guid CurrentUserId, string Type);
    }
}
