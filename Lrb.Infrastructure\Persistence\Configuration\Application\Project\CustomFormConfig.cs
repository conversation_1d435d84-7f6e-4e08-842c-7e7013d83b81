﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Project
{
    public class CustomFormConfig : IEntityTypeConfiguration<CustomFormFields>
    {
        public void Configure(EntityTypeBuilder<CustomFormFields> builder)
        {
            builder.IsMultiTenant();
        }
    }
}