﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Dtos
{
    public class LeadFilterDto
    {
            public BaseLeadVisibility LeadVisibility { get; set; }
            public List<LeadTagEnum>? LeadTags { get; set; }
            public LeadFilterTypeWeb FilterType { get; set; }
            public List<EnquiryType>? EnquiredFor { get; set; }
            public DateType? DateType { get; set; }
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }
            public List<Guid>? AssignTo { get; set; }
            public List<LeadSource>? Source { get; set; } = new();
            public List<Budget>? Budget { get; set; }
            public List<string>? Projects { get; set; }
            public List<string>? Properties { get; set; }
            public List<double>? NoOfBHKs { get; set; }
            public List<BHKType>? BHKTypes { get; set; }
            public List<Guid>? PropertyType { get; set; }
            public List<Guid>? PropertySubType { get; set; }
            public List<Guid>? StatusIds { get; set; }
            public string? LeadSearch { get; set; }
            public List<string>? Locations { get; set; }
            public List<MeetingOrVisitCompletionStatus>? MeetingOrVisitStatuses { get; set; }
            public long? MinBudget { get; set; }
            public long? MaxBudget { get; set; }
            public bool? IsWithTeam { get; set; }
            public DateTime? ToDateForMeetingOrVisit { get; set; }
            public DateTime? FromDateForMeetingOrVisit { get; set; }
            public List<string>? SubSources { get; set; }
            public List<Guid>? IntegrationAccountIds { get; set; }
            public List<string>? AgencyNames { get; set; }
            public LeadTagFilterDto? TagFilterDto { get; set; }
            public List<Guid>? CreatedByIds { get; set; }
            public List<Guid>? LastModifiedByIds { get; set; }
            public List<Guid>? LastDeletedByIds { get; set; }   
            public List<Guid>? AssignedFromIds { get; set; }
            public List<Guid>? ArchivedByIds { get; set; }
            public List<string>? CompanyNames { get; set; }
            public string? CompanyName { get; set; }
            public List<Guid>? RestoredByIds { get; set; }
            public double? CarpetArea { get; set; }
            public Guid CarpetAreaUnitId { get; set; }
            public float? ConversionFactor { get; set; }
            public string? SearchByNameOrNumber { get; set; }
            public List<Guid>? SubStatusIds { get; set; }
            public List<Guid>? LeadIds { get; set; }
            public List<string>? SerialNumbers { get; set; }
            public string? ReferralName { get; set; }
            public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }

        public List<Guid>? ClosingManagers { get; set; }
            public List<Guid>? SourcingManagers { get; set; }
            public List<Profession>? Profession { get; set; }
            public List<string>? Zones { get; set; }
            public List<string>? Cities { get; set; }
            public List<string>? States { get; set; }
            public string? IsUntouched { get; set; }
            public List<Guid>? HistoryAssignedToIds { get; set; }
            public List<Guid>? SecondaryUsers { get; set; }
            public List<Guid>? SecondaryFromIds { get; set; }
            public List<Guid>? DoneBy { get; set; }
            public bool? IsWithHistory { get; set; }
    }
    public class LeadFormettedFilterDto
    {
        public string? AssignTo { get; set; }
        public string? AssignedFrom { get; set; }
        public string? Status { get; set; }
        public string? SubStatus { get; set; }
        public string? Source { get; set; } 
        public string? SubSources { get; set; }
        public string? EnquiredFor { get; set; }
        public string? AgencyNames { get; set; }
        public string? PropertyType { get; set; }
        public string? PropertySubType { get; set; }
        public string? NoOfBHKs { get; set; }
        public string? BHKTypes { get; set; }
        public string? Locations { get; set; }
        public string? Zones { get; set; }
        public string? Cities { get; set; }
        public string? Projects { get; set; }
        public string? Properties { get; set; }
        public string? RestoredBy { get; set; }
        public string? Profession { get; set; }
        public double? CarpetArea { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public bool? IsWithTeam { get; set; }
        public string? CreatedBy { get; set; }
        public string? LastModifiedBy { get; set; }
        public string? LastDeletedBy { get; set; }
        public string? IsUntouched { get; set; }
        public string? HistoryAssignedToIds { get; set; }
        public string? SecondaryUsers { get; set; }
        public string? SecondaryFromIds { get; set; }
        public string? DoneBy { get; set; }
        public bool? IsWithHistory { get; set; }
    }

    public class AssignedUserDetailsDto
    {
        public string? Id { get; set; }
        public string? UserName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? FullName { get; set; }
    }
}
