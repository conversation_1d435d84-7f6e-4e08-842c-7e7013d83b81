﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.Dtos.SalesDashboardTracker
{
    public class SalesDashboardTrackerDto
    {
        public string? Id { get; set; }
        public string? UserName { get; set; }

        // Appointments metrics
        public long? MeetingDone_12AM_9AM { get; set; }
        public long? MeetingDone_9AM_11AM { get; set; }
        public long? MeetingDone_11AM_1PM { get; set; }
        public long? MeetingDone_1PM_4PM { get; set; }
        public long? MeetingDone_4PM_7PM { get; set; }
        public long? MeetingDone_7PM_12AM { get; set; }
        public long? TotalMeetingDone { get; set; }

        // Communications metrics
        public long? Communications_12AM_9AM { get; set; }
        public long? Communications_9AM_11AM { get; set; }
        public long? Communications_11AM_1PM { get; set; }
        public long? Communications_1PM_4PM { get; set; }
        public long? Communications_4PM_7PM { get; set; }
        public long? Communications_7PM_12AM { get; set; }
        public long? TotalCommunications { get; set; }

        // Bookings metrics (Agreement Values)
        public decimal? Bookings_12AM_9AM { get; set; }
        public decimal? Bookings_9AM_11AM { get; set; }
        public decimal? Bookings_11AM_1PM { get; set; }
        public decimal? Bookings_1PM_4PM { get; set; }
        public decimal? Bookings_4PM_7PM { get; set; }
        public decimal? Bookings_7PM_12AM { get; set; }
        public decimal? TotalAgreementValue { get; set; }

        // Scheduled meetings metrics
        public long? Scheduled_12AM_9AM { get; set; }
        public long? Scheduled_9AM_11AM { get; set; }
        public long? Scheduled_11AM_1PM { get; set; }
        public long? Scheduled_1PM_4PM { get; set; }
        public long? Scheduled_4PM_7PM { get; set; }
        public long? Scheduled_7PM_12AM { get; set; }
        public long? TotalScheduled { get; set; }
    }

}
