﻿using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities;


/// <summary>
/// Facebook Account Login Response (primary)
/// Should be unique in whole application
/// No account can be used with more than one tenant
/// </summary>
public class FacebookAuthResponse : AuditableEntity, IAggregateRoot
{
    public string AccessToken { get; set; } = default!;
    public string FacebookUserId { get; set; } = default!;
    public string FacebookAccountName { get; set; } = default!;
    public string LongLivedUserAccessToken { get; set; } = default!;

    //Foreign key references
    public IList<FacebookConnectedPageAccount>? FBConnectedPageAccounts { get; set; }
    public int FbLeadCount { get; set; }
    public int InstaLeadCount { get; set; }
    public bool IsActive { get; set; }
    public UserAssignment? UserAssignment { get; set; }
    public IntegrationAssignment? Assignment { get; set; }
    public string? ConversionsAccessToken { get; set; }
    public string? PixelId { get; set; }
    public List<CustomMasterLeadStatus>? Statuses { get; set; }
    [Column(TypeName = "jsonb")]
    public Dictionary<MetaLeadUnifiedStatus, List<string>>? MetaLeadStatusMapping { get; set; }
}

/// <summary>
/// The /me/accounts endpoint returns a list of all the accounts that a user is able to manage, 
/// including Facebook pages, apps, and groups. This is a facebook page account.
/// </summary>
public class FacebookConnectedPageAccount : AuditableEntity, IAggregateRoot
{
    public string LongLivedPageAccessToken { get; set; } = default!;
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("access_token")]
    public string AccessToken { get; set; } = default!;
    [JsonProperty("category")]
    public string Category { get; set; } = default!;
    [JsonProperty("name")]
    public string Name { get; set; } = default!;
    /// <summary>
    /// Facebook id for the page (ad account)
    /// </summary>
    [JsonProperty("fb_id")]
    public string FacebookId { get; set; } = default!;
    [JsonProperty("page_token")]
    public string PageToken { get; set; } = default!;

    //Foreign key references
    public Guid FacebookAuthResponseId { get; set; }
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public FacebookAuthResponse FacebookAuthResponse { get; set; } = default!;
    public IList<FacebookLeadGenForm>? FBLeadGenForms { get; set; }
    public UserAssignment? UserAssignment { get; set; }
}

public class FacebookLeadGenForm : AuditableEntity, IAggregateRoot
{
    [JsonProperty("leads_count")]
    public int LeadsCount { get; set; }
    [JsonProperty("fb_id")]
    public string FacebookId { get; set; } = default!;
    [JsonProperty("name")]
    public string Name { get; set; } = default!;
    [JsonProperty("page_id")]
    public string PageId { get; set; } = default!;
    [JsonProperty("status")]
    public string Status { get; set; } = default!;
    public bool IsSubscribed { get; set; }
    public string? AgencyName { get; set; }
    public Guid AutomationId { get; set; }
    public bool IsAutomated { get; set; }
    //Foreign key references
    public Guid FacebookConnectedPageAccountId { get; set; }
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public FacebookConnectedPageAccount FacebookConnectedPageAccount { get; set; } = default!;
    public UserAssignment? UserAssignment { get; set; }
    public IntegrationAssignment? Assignment { get; set; }
    public int TotalLeadsCount { get; set; }
    public Agency? Agency { get; set; }
    public string? CountryCode { get; set; }
    public Campaign? CustomCampaign { get; set; }
}

public class FacebookAdsInfo : AuditableEntity, IAggregateRoot
{


    public string? AdId { get; set; }
    public string? AdName { get; set; }
    public string? Status { get; set; }
    public string? PageId { get; set; }
    public string? AdSetName { get; set; }
    public string? AdSetId { get; set; }
    public string? CampaignName { get; set; }
    public string? CampaignId { get; set; }
    public string? AdAccountName { get; set; }
    public string? AdAccountId { get; set; }
    public string? AgencyName { get; set; }
    public Agency? Agency { get; set; }
    public Guid AutomationId { get; set; }
    public bool IsAutomated { get; set; }
    public Guid FacebookAuthResponseId { get; set; }
    public bool IsSubscribed { get; set; }
    public UserAssignment? UserAssignment { get; set; }
    public IntegrationAssignment? Assignment { get; set; }
    public int LeadsCount { get; set; }
    public string? CountryCode { get; set; }
    public Campaign? CustomCampaign { get; set; }
}


public class FacebookLeadInfo : AuditableEntity<string>, IAggregateRoot
{
    [JsonProperty("id")]
    [JsonPropertyName("id")]
    public override string Id { get; set; } = default!;

    [JsonProperty("platform")]
    [JsonPropertyName("platform")]
    public string? Platform { get; set; }

    [JsonProperty("ad_id")]
    [JsonPropertyName("ad_id")]
    public string? AdId { get; set; }

    [JsonProperty("ad_name")]
    [JsonPropertyName("ad_name")]
    public string? AdName { get; set; }

    [JsonProperty("adset_id")]
    [JsonPropertyName("adset_id")]
    public string? AdsetId { get; set; }


    [JsonProperty("adset_name")]
    [JsonPropertyName("adset_name")]
    public string? AdsetName { get; set; }

    [JsonProperty("campaign_id")]
    [JsonPropertyName("campaign_id")]
    public string? CampaignId { get; set; }

    [JsonProperty("campaign_name")]
    [JsonPropertyName("campaign_name")]
    public string? CampaignName { get; set; }

    [JsonProperty("created_time")]
    [JsonPropertyName("created_time")]
    public DateTime CreatedTime { get; set; }

    [JsonProperty("field_data")]
    [JsonPropertyName("field_data")]
    [Column(TypeName = "jsonb")]
    public List<FieldData>? FieldData { get; set; }

    [JsonProperty("form_id")]
    [JsonPropertyName("form_id")]
    public string? FormId { get; set; }

    [JsonProperty("is_organic")]
    [JsonPropertyName("is_organic")]
    public bool IsOrganic { get; set; }
}

public class FieldData
{
    [JsonProperty("name")]
    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonProperty("values")]
    [JsonPropertyName("values")]
    public List<string>? Values { get; set; }
}


//TBD
//Subscription via Associated Page or Individual Page (Limit of API calls to decide which subscription model to chosse
//i.e. iterate through all the lead_gen_forms by associated_page and sbuscribe or only by associated_page

//Column Mapping depends on what subcription model, if via lead_gen_forms, will fetch columns of all 

//public class Question
//{
//    [JsonProperty("key")]
//    public string key;

//    [JsonProperty("label")]
//    public string label;

//    [JsonProperty("type")]
//    public string type;

//    [JsonProperty("id")]
//    public string id;
//}

//public class LeadGenFormFields
//{
//    [JsonProperty("id")]
//    public string id;

//    [JsonProperty("name")]
//    public string name;

//    [JsonProperty("questions")]
//    public List<Question> questions;
//}