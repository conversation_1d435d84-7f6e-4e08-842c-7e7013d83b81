using Lrb.Application.CustomForm.Web.Dtos;
using Lrb.Domain.Entities;

namespace Lrb.Application.CustomForm.Web.Requests
{
    public class CreateCustomFormRequest :  IRequest<Response<bool>>
    {
        public List<CreateCustomFormDto>? Fields { get; set; }
    }

    public class CreateCustomFormRequestHandler : IRequestHandler<CreateCustomFormRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;

        public CreateCustomFormRequestHandler(IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo)
        {
            _customFormRepo = customFormRepo;
        }
        public async Task<Response<bool>> Handle(CreateCustomFormRequest request, CancellationToken cancellationToken)
        {
            if (request?.Fields == null)
            {
                throw new InvalidOperationException("Fields is required");
            }
            var customForms = request.Fields.Select(field => field.Adapt<Domain.Entities.CustomFormFields>()).ToList();

            await _customFormRepo.AddRangeAsync(customForms, cancellationToken);
            return new Response<bool>(true);


        }

    }
}
