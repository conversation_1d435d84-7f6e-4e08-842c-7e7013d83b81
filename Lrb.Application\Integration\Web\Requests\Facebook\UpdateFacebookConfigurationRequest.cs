﻿using Lrb.Application.CustomStatus.Web;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class UpdateFacebookConfigurationRequest : IRequest<Response<bool>>
    {
        public string? FacebookUserId { get; set; }
        public List<Guid>? StatusIds { get; set; }
        public string? ConversionAccessToken { get; set; }
        public string? PixelId { get; set; }
        public Dictionary<MetaLeadUnifiedStatus, List<string>>? MetaLeadStatusMapping { get; set; }

    }
    public class UpdateFacebookConfigurationRequestHandler : IRequestHandler<UpdateFacebookConfigurationRequest, Response<bool>>
    {

        private readonly IRepositoryWithEvents<FacebookAuthResponse> _facebookAuthResponseRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMasterLeadStatusRepo;

        public UpdateFacebookConfigurationRequestHandler(IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMasterLeadStatusRepo)
        {
            _facebookAuthResponseRepo = facebookAuthResponseRepo;
            _customMasterLeadStatusRepo = customMasterLeadStatusRepo;
        }

        public async Task<Response<bool>> Handle(UpdateFacebookConfigurationRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (!string.IsNullOrEmpty(request.FacebookUserId) && !string.IsNullOrEmpty(request.ConversionAccessToken))
                {
                    var fbAuthResponse = await _facebookAuthResponseRepo.FirstOrDefaultAsync(new GetFacebookAuthResponseByFBIdSpec(request.FacebookUserId ?? string.Empty));
                    var statuses = await _customMasterLeadStatusRepo.ListAsync(new GetAllStatusByIdSpec(request.StatusIds ?? new()));
                    
                    fbAuthResponse.PixelId = request.PixelId ?? string.Empty;
                    fbAuthResponse.ConversionsAccessToken = request.ConversionAccessToken ?? fbAuthResponse.ConversionsAccessToken;
                    fbAuthResponse.MetaLeadStatusMapping = request.MetaLeadStatusMapping ?? fbAuthResponse.MetaLeadStatusMapping;
                    await _facebookAuthResponseRepo.UpdateAsync(fbAuthResponse);
                    return new(true);
                }
            }
            catch (Exception ex) { }
            return new(false);
        }
    }
}
