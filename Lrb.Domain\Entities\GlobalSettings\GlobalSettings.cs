﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class GlobalSettings : AuditableEntity, IAggregateRoot
    {
        public string? NotificationSettings { get; set; }
        public string? CallSettings { get; set; }
        public bool HasInternationalSupport { get; set; }
        public bool HasDailyStatusFeatureEnabled { get; set; }
        public bool IsLeadStatusToPendingUpdatesEnabled { get; set; }
        public bool IsLeadsExportEnabled { get; set; }
        public bool IsLeadSourceEditable { get; set; }
        public bool IsInstagramSourceEnabled { get; set; }
        public bool IsWhatsAppEnabled { get; set; }
        public DateTime? DayStartTime { get; set; }
        public DateTime? DayEndTime { get; set; }
        public bool IsCallDetectionActivated { get; set; }
        public bool IsGoogleMapLocationEnabled { get; set; }
        public bool IsZoneLocationEnabled { get; set; }
        public string? LeadNotesSetting { get; set; }
        public bool IsMaskedLeadContactNo { get; set; }
        public bool IsPropertiesExportEnabled { get; set; }
        public bool IsPropertyAssignmentEnabled { get; set; }
        public bool IsMicrositeFeatureEnabled { get; set; }
        public bool IsExportDataEnabled { get; set; }
        public bool IsDualOwnershipEnabled { get; set; }
        public bool IsStickyAgentEnabled { get; set; }
        public string? LeadPropertySetting { get; set; }
        public string? LeadProjectSetting { get; set; }
        public string? OTPSettings { get; set; }
        public bool IsTimeZoneEnabled { get; set; }
        public IList<CountryInfo>? Countries { get; set; }
        public bool IsCopyPasteEnabled { get; set; }
        public bool IsScreenshotEnabled { get; set; }
        public bool IsProjectMicrositeEnabled { get; set; }
        public bool IsLeadRotationEnabled { get; set; }
        public bool IsStickyAgentOverriddenEnabled { get; set; }
        public bool IsCustomStatusEnabled { get; set; }
        public bool IsWhatsAppDeepIntegration { get; set; }
        public DirectionOfLeadCreation DirectionOfLeadCreation { get; set; }
        public DirectionOfLeadModification DirectionOfLeadModification { get; set; }
        public bool ShouldHideDashBoard {  get; set; }
        public bool IsAssignedCallLogsEnabled { get; set; }
        public bool IsTeamLeadRotationEnabled { get; set; }
        public bool ShouldEnablePropertyListing { get; set; }
        public bool CanAccessAnonymousApis { get; set; }
        public bool IsCustomLeadFormEnabled { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<string,string>? DefaultValues { get; set; } 
        public bool? IsEngageToEnabled { get; set; }
        public bool? IsMobileCallEnabled { get; set; }
        public bool IsProjectsExportEnabled { get; set; }
        public bool EnableFacebookConversion {  get; set; }
        public bool IsReportsConfigurationEnabled { get; set; }
        public bool ShouldEnableEnquiryForm {  get; set; } = true;
        public bool ShowMoreMicrositeProperties {  get; set; }
        public bool? ShouldRenameSiteVisitColumn { get; set; } = false;
        public bool? IsPastDateSelectionEnabled { get; set; }
        public string? GeneralSettings { get; set; }
        public bool? ShowCustomProjectFields { get; set; }  
    }
}
