﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class lengthandbredth : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "Breadth",
                schema: "LeadratBlack",
                table: "Projects",
                type: "double precision",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Length",
                schema: "LeadratBlack",
                table: "Projects",
                type: "double precision",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Breadth",
                schema: "LeadratBlack",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "Length",
                schema: "LeadratBlack",
                table: "Projects");
        }
    }
}
