﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Attendance\Mobile\Mappings\**" />
		<Compile Remove="Attendance\Mobile\Validations\**" />
		<Compile Remove="Property\Mappings\**" />
		<Compile Remove="Reports\Mobile\**" />
		<Compile Remove="Reports\Web\Validators\**" />
		<EmbeddedResource Remove="Attendance\Mobile\Mappings\**" />
		<EmbeddedResource Remove="Attendance\Mobile\Validations\**" />
		<EmbeddedResource Remove="Property\Mappings\**" />
		<EmbeddedResource Remove="Reports\Mobile\**" />
		<EmbeddedResource Remove="Reports\Web\Validators\**" />
		<None Remove="Attendance\Mobile\Mappings\**" />
		<None Remove="Attendance\Mobile\Validations\**" />
		<None Remove="Property\Mappings\**" />
		<None Remove="Reports\Mobile\**" />
		<None Remove="Reports\Web\Validators\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Amazon.Extensions.CognitoAuthentication" Version="2.2.3" />
		<PackageReference Include="Ardalis.Specification" Version="6.1.0" />
		<PackageReference Include="AWS.Logger.SeriLog" Version="3.2.0" />
		<PackageReference Include="AWSSDK.Batch" Version="3.7.102.49" />
		<PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.200.3" />
		<PackageReference Include="AWSSDK.Pinpoint" Version="3.7.100.34" />
		<PackageReference Include="DocumentFormat.OpenXml" Version="2.16.0" />
		<PackageReference Include="EPPlus" Version="6.1.3" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.2.2" />
		<PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
		<PackageReference Include="Google.Apis.Drive.v3" Version="1.61.0.3110" />
		<PackageReference Include="Google.Apis.Sheets.v4" Version="1.61.0.3092" />
		<PackageReference Include="ISO3166" Version="1.0.4" />
		<PackageReference Include="libphonenumbers-dotnet" Version="1.1.0" />
		<PackageReference Include="Mapster" Version="7.3.0" />
		<PackageReference Include="Mapster.DependencyInjection" Version="1.0.0" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
		<PackageReference Include="Microsoft.Azure.Cosmos" Version="3.37.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.10" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="6.0.10" />
		<PackageReference Include="Microsoft.Graph" Version="4.48.0" />
		<PackageReference Include="Nager.Country" Version="4.0.0" />
		<PackageReference Include="NodaTime" Version="3.1.11" />
		<PackageReference Include="Npgsql" Version="6.0.7" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="6.0.0" />
		<PackageReference Include="QRCoder" Version="1.4.3" />
		<PackageReference Include="Serilog.AspNetCore" Version="6.0.1" />
		<PackageReference Include="RestSharp" Version="108.0.2" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.4" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.23.1" />
		<PackageReference Include="AWSSDK.SecretsManager" Version="3.7.100.6" />
		<PackageReference Include="Refit" Version="6.3.2" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Lrb.Domain\Lrb.Domain.csproj" />
		<ProjectReference Include="..\Lrb.Shared\Lrb.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Attendance\Mobile\Mappings\" />
		<Folder Include="Attendance\Mobile\Validations\" />
		<Folder Include="Common\CustomAttribute\" />
		<Folder Include="Common\Email\" />
		<Folder Include="Common\PushNotificationService\" />
		<Folder Include="Common\WhatsApp\Abracket\Dtos\" />
		<Folder Include="Common\WhatsApp\Interakt\Mappings\" />
		<Folder Include="CustomAddress\Web\Community\Mapping\" />
		<Folder Include="CustomAddress\Web\SubCommunity\Mapping\" />
		<Folder Include="CustomAddress\Web\TowerName\Mapping\" />
		<Folder Include="CustomForm\Mobile\Dtos\" />
		<Folder Include="CustomForm\Mobile\Requests\" />
		<Folder Include="CustomForm\Mobile\Specs\" />
		<Folder Include="Dashboard\Web\Validators\" />
		<Folder Include="DeviceInfo\Mobile\Dtos\" />
		<Folder Include="DeviceInfo\Mobile\Specs\" />
		<Folder Include="Identity\Helper\" />
		<Folder Include="Media\Mobile\Mappings\" />
		<Folder Include="Media\Mobile\Validators\" />
		<Folder Include="Media\Web\" />
		<Folder Include="Property\Web\V2\Validations\" />
		<Folder Include="PushNotification\Dtos\" />
		<Folder Include="PushNotification\Requests\" />
		<Folder Include="PushNotification\Specs\" />
		<Folder Include="Reports\Web\Data\Common\" />
		<Folder Include="Reports\Web\Data\Specs\" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="HangfireJobs\" />
	</ItemGroup>

</Project>
