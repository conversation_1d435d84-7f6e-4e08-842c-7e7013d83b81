using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Application.Reports;
using Lrb.Application.Reports.Web;
using Lrb.Application.Reports.Web.Dtos.SalesDashboardTracker;
using Lrb.Application.Reports.Web.Lead.Dtos.ReportsCofig;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSource;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSubSource;
using Lrb.Application.Reports.Web.Lead.Requests;
using Lrb.Application.Reports.Web.Lead.Requests.ReportConfig;
using Lrb.Application.Reports.Web.Requests;
using Lrb.Application.Reports.Web.Requests.DatewiseSourceCount;
using Lrb.Application.Reports.Web.Requests.DatewisesourceR;
using Lrb.Application.Reports.Web.Requests.ProjectvsSubStatus;
using Lrb.Application.Reports.Web.Requests.SalesDashboardTracker;
using Lrb.Application.Reports.Web.Requests.SubSource;
using Lrb.Application.Reports.Web.Requests.User.CallLog;
using Lrb.Application.Reports.Web.UserVsSource.Requests;



namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class ReportController : VersionedApiController
    {
        [HttpGet("user/tags")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead tags report based on users", "")]
        public async Task<PagedResponse<LeadTagsUserDto, string>> GetReportAsync([FromQuery] GetTagsReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/tags/export")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead tags report based on users", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForTagsReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.ViewAllUsers, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on users", "")]
        public async Task<PagedResponse<LeadsReportByUserDto, string>> GetReportAsync([FromQuery] GetLeadStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }

        #region v2 status 
        [HttpGet("user/new/status-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on users", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetLeadStatusReportByUsersCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/new/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on users", "")]
        public async Task<PagedResponse<LeadsReportByUserDto, string>> GetReportAsync([FromQuery] V2GetLeadStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("user/status/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.ExportAllUsers, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report based on users", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForLeadStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("user/status/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report by email based on users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForLeadStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/meetingandvisit")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get meeting and site visit report based on users", "")]
        public async Task<PagedResponse<LeadAppointmentByUserDto, string>> GetReportAsync([FromQuery] GetVisitAndMeetingReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/meetingandvisit/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export meeting and site visit report based on users", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForVisitAndMeetingReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("user/meetingandvisit/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export meeting and site visit report by email based on users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForVisitAndMeetingReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("project/tags")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead tags report based on projects", "")]
        public async Task<PagedResponse<LeadTagsByProjectDto, string>> GetReportAsync([FromQuery] GetTagsReportByProjectRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("project/tags/export")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead tags report based on projects", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForLeadTagsReportByProjectRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("project/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on projects", "")]
        public async Task<PagedResponse<ProjectReportDto, string>> GetReportAsync([FromQuery] GetLeadStatusReportByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }

        #region v2 status 
        [HttpGet("project/status/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on projects", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetLeadStatusReportByProjectsCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("project/status/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on projects", "")]
        public async Task<PagedResponse<ProjectReportDto, string>> GetReportAsync([FromQuery] V2GetLeadStatusReportByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("project/status/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report based on projects", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForLeadStatusReportByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/status/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report by email based on projects", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForLeadStatusReportByProjectsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("project/meetingandvisit")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get meeting and site visit report based on projects", "")]
        public async Task<PagedResponse<LeadAppointmentByProjectDto, string>> GetReportAsync([FromQuery] GetVisitAndMeetingReportByProjectRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("project/meetingandvisit/export")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export meeting and site visit report based on projects", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForVisitAndMeetingReportByProjectRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("source/tags")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead tags report based on source", "")]
        public async Task<PagedResponse<LeadTagsSourceDto, string>> GetReportAsync([FromQuery] GetTagsReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("source/tags/export")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead tags report based on source", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForTagsReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("source/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on source", "")]
        public async Task<PagedResponse<LeadsSourceReportDto, string>> GetReportAsync([FromQuery] GetLeadStatusReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }

        #region v2 source 
        [HttpGet("source/status/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on source", "")]
        public async Task<PagedResponse<LeadsSourceReportDto, string>> GetReportAsync([FromQuery] V2GetLeadStatusReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("source/status/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on source", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetLeadStatusReportBySourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("source/status/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report based on source", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForLeadStatusReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("source/status/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report by email based on source", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForLeadStatusReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("source/meetingandvisit")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get meeting and site visit report based on source", "")]
        public async Task<PagedResponse<LeadAppointmentBySourceDto, string>> GetReportAsync([FromQuery] GetVisitAndMeetingReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("source/meetingandvisit/export")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export meeting and site visit report based on source", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForVisitAndMeetingReportBySourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("export/tracker")]
        [TenantIdHeader]
        // [MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get all export reports trackers", "")]
        public async Task<PagedResponse<ExportReportsTrackerDto, string>> GetReportAsync([FromQuery] GetAllReportTrackerRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("subsource/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on sub source", "")]
        public async Task<PagedResponse<LeadsSubSourceReportDto, string>> GetReportAsync([FromQuery] GetLeadStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }

        #region v2 subsource/status
        [HttpGet("subsource/status/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on sub source", "")]
        public async Task<PagedResponse<LeadsSubSourceReportDto, string>> GetReportAsync([FromQuery] V2GetLeadStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("subsource/status/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on sub source", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetLeadStatusReportBySubSourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("subsource/status/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report based on sub source", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForLeadStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("subsource/status/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report by email based on sub source", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForLeadStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("agency/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on agency name", "")]
        public async Task<PagedResponse<LeadsAgencyReportDto, string>> GetReportAsync([FromQuery] GetLeadStatusReportByAgencyRequest request)
        {
            return await Mediator.Send(request);
        }

        #region v2 agency/status
        [HttpGet("agency/status/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report based on agency name", "")]
        public async Task<PagedResponse<LeadsAgencyReportDto, string>> GetReportAsync([FromQuery] V2GetLeadStatusReportByAgencyRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("agency/status/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get lead status report count based on agency name", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetLeadStatusReportByAgencyCountRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("agency/status/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report based on agency name", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForLeadStatusReportByAgencyRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("agency/status/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export lead status report by email based on agency name", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForLeadStatusReportByAgencyRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportDto, string>> GetReportAsync([FromQuery] GetActivityReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export Activity Report of all Users", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForActivityReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("activity/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export Activity Report of all Users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForActivityReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("substatus")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report of all Users", "")]
        public async Task<PagedResponse<ModifiedSubStatusReportDto, string>> GetReportAsync([FromQuery] GetSubStatusReportRequest request)
        {
            return await Mediator.Send(request);
        }

        #region v2 substatus 
        [HttpGet("substatus/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report of all Users", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromQuery] V2GetSubStatusReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("substatus/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report count of all Users", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetSubStatusReportCountRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("substatus/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export Sub-Status Report of all Users", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForSubStatusReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("substatus/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export Sub-Status Report of all Users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForSubStatusReportRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("substatus/bysubsource")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report by Sub-Source", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromQuery] GetSubStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }


        #region v2 substatus/bysubsource 
        [HttpGet("substatus/bysubsource/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report by Sub-Source", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromQuery] V2GetSubStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("substatus/bysubsource/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status Report count by Sub-Source", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetSubStatusReportBySubSourceCountRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("substatus/bysubsource/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export Sub-Status Report by Sub-Source.", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForSubStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("substatus/bysubsource/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export Sub-Status Report by Sub-Source - With Email.", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForSubStatusReportBySubSourceRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("project/bysubstatus")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Project Report by Sub-Status ", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromQuery] GetProjectReportBySubStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        #region v2 project/bysubstatus 
        [HttpGet("project/bysubstatus/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Project Report by Sub-Status", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GetReportAsync([FromQuery] V2GetProjectReportBySubStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("project/bysubstatus/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Project Report count by Sub-Status", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetProjectReportBySubStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpGet("project/bysubstatus/export")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export Project Report by Sub-Status.", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForProjectReportBySubStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("project/bysubstatus/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export Project Report by Sub-Status - With Email.", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForProjectReportBySubStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/call-log")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.ViewAllUsers, LrbResource.Reports)]
        [OpenApiOperation("Get call-log report based on users", "")]
        public async Task<PagedResponse<ViewCallLogReportDto, string>> GetReportAsync([FromQuery] GetCallLogReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }

        #region v2 user/call
        [HttpGet("user/call-log/new")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get call-log report based on users", "")]
        public async Task<PagedResponse<ViewCallLogReportDto, string>> GetReportAsync([FromQuery] V2GetCallLogReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/call-log/new-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get call-log report based on users count", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetCallLogReportByUserCountRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        [HttpPost("user/call-log/export/email")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Export, LrbResource.Reports)]
        [OpenApiOperation("Export call-log report by email based on users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForCallLogReportByUserReportRequest request)
        {
            return await Mediator.Send(request);
        }

        #region v2 meetingandvisit
        [HttpGet("user/new/meetingandvisit")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get meeting and site visit report based on users", "")]
        public async Task<PagedResponse<LeadAppointmentByUserDto, string>> GetReportAsync([FromQuery] V2GetVisitAndMeetingReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/new/meetingandvisit-count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get meeting and site visit report count based on users", "")]
        public async Task<int> GetReportAsync([FromQuery] V2GetVisitAndMeetingReportByUserCountRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        #region v2 activity
        [HttpGet("activity-count")]
        [TenantIdHeader]
        [OpenApiOperation("View activity report count of all users", "")]
        public async Task<int> GetReportAsync([FromQuery] GetActivityReportCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level1")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel1Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel1Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level2")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel2Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel2Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level3")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel3Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel3Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level4")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel4Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel4Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level5")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel5Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel5Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level6")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel6Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel6Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level7")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel7Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel7Request request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("activity/flags")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityFlagsDto, string>> GetReportAsync([FromQuery] GetFlagsActivityReportRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion



        #region v2 activity
        [HttpGet("activity/level9")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel8Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel8Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level10")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel9Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel9Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level11")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel10Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel10Request request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("activity/level12")]
        [TenantIdHeader]
        [OpenApiOperation("View Activity Report of all Users", "")]
        public async Task<PagedResponse<UserActivityReportLevel11Dto, string>> GetReportAsync([FromQuery] GetActivityReportLevel11Request request)
        {
            return await Mediator.Send(request);
        }
        #endregion



        [HttpGet("substatus/export/updated")]
        [TenantIdHeader]
        [OpenApiOperation("Export Sub-Status Report of all Users", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForSubStatusnewReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("project/bysubstatus/updated")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Project Report by Sub-Status ", "")]
        public async Task<PagedResponse<ModifiedProjectvsSubStatusReportDto, string>> GetReportAsync([FromQuery] GetProjectReportvsSubStatusnewRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("substatus/bysubsource/updated")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Sub-Status vs Subsource Report of all Users", "")]
        public async Task<PagedResponse<ModifiedSubStatusvsSubSourceReportDto, string>> GetReportAsync([FromQuery] GetSubStatusvsSubSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }


        #region Custom  
        #region Agency
        [HttpPost("agency/status/export/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Export lead status report by email based on agency name", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForAgencyReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("agency/status/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on agency name", "")]
        public async Task<PagedResponse<ViewAgencyReportDto, string>> GetReportAsync([FromQuery] GetAgencyReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("agency/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report count based on agency name", "")]
        public async Task<int> GetReportAsync([FromQuery] GetAgencyReportByStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        #region Project
        [HttpGet("project/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on projects", "")]
        public async Task<int> GetReportAsync([FromQuery] ProjectReportByStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("project/status/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report count based on projects", "")]
        public async Task<PagedResponse<ViewProjectReportDto, string>> GetReportAsync([FromQuery] ProjectReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("project/status/export/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Export lead status report by email based on projects", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForProjectReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        #region Source
        [HttpGet("source/status/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on source", "")]
        public async Task<PagedResponse<ViewSourceReportDto, string>> GetReportAsync([FromQuery] SourceReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("source/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report count based on source", "")]
        public async Task<int> GetReportAsync([FromQuery] SourceReportByStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("source/status/export/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Export lead status report by email based on source", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForSourceReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        #region SubSource
        [HttpGet("subsource/status/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on sub source", "")]
        public async Task<PagedResponse<ViewSubSourceReportDto, string>> GetReportAsync([FromQuery] SubSourceReportByStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("subsource/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report count based on sub source", "")]
        public async Task<int> GetReportAsync([FromQuery] SubSourceReportByStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("subsource/status/export/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Export lead status report by email based on sub source", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForSubSourceReportByLeadStatusRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion

        #region User
        [HttpGet("user/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on users", "")]
        public async Task<int> GetReportAsync([FromQuery] GetUserCountReportRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("user/status/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report count based on users", "")]
        public async Task<PagedResponse<ViewUserReportDto, string>> GetReportAsync([FromQuery] LeadStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("user/status/export/custom")]
        [TenantIdHeader]
        [OpenApiOperation("Export lead status report by email based on users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForStatusReportByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        #endregion
        #endregion

        [HttpGet("user/meetingandvisit/level1")]
        [TenantIdHeader]
        [OpenApiOperation("View Meeting and Visit Report of all Users", "")]
        public async Task<PagedResponse<LeadAppointmentByUserV3Dto, string>> GetReportAsync([FromQuery] V3GetMeetingAndSitevisitReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("user/meetingandvisit/level2")]
        [TenantIdHeader]
        [OpenApiOperation("View Meeting and Visit Report of all Users", "")]
        public async Task<PagedResponse<LeadAppointmentByUserV4Dto, string>> GetReportAsync([FromQuery] V4GetMeetingAndSitevisitReportByUserRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("user/meetingandvisit/count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("Get meeting and site visit report count based on users", "")]
        public async Task<int> GetReportAsync([FromQuery] V3GetVisitAndMeetingReportByUserCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("datewisesource")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Leads Source Count Based on Created Date .", "")]
        public async Task<PagedResponse<Dictionary<string, object>, string>> GeLeadsByYear([FromQuery] GetLeadDateBySourceCountRequest request)
        {
            return await (Mediator.Send(request));
        }
        [HttpGet("datewisesource/export")]
        [TenantIdHeader]
        [OpenApiOperation("Export datewise source report based on users", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForDatewiseSourceReportrRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("datewisesource/export/email")]
        [TenantIdHeader]
        [OpenApiOperation("Export datewise source report by email based on users", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForDatewiseSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("datewisesource/count")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Reports)]
        [OpenApiOperation("View Datewise Source Report Count", "")]
        public async Task<int> GetReportAsync([FromQuery] GetDatewiseSourceReportCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("uservssource")]
        [TenantIdHeader]
        [OpenApiOperation("view uservssource report ", "")]
        public async Task<PagedResponse<SourceReportByUserDto, string>> GetReportAsync([FromQuery] GetUserVsSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("uservssource/count")]
        [TenantIdHeader]
        [OpenApiOperation("Get uservssource report count ", "")]
        public async Task<int> GetReportAsync([FromQuery] GetUserVsSourceReportCountRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("uservssource/export")]
        [TenantIdHeader]
        [OpenApiOperation("Export uservssource report", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForUserVsSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("uservssource/export/email")]
        [TenantIdHeader]
        [OpenApiOperation("Export uservssource report by email", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForUserVsSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("uservssubsource")]
        [TenantIdHeader]
        [OpenApiOperation("view uservssubsource report", "")]
        public async Task<PagedResponse<UservsSubSourceReportDto, string>> GetReportAsync([FromQuery] GetUserVsSubSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("uservssubsource/count")]
        [TenantIdHeader]
        [OpenApiOperation("Get  uservssubsource report count", "")]
        public async Task<int> GetReportAsync([FromQuery] GetUserVsSubSourceReportCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("uservssubsource/export")]
        [TenantIdHeader]
        [OpenApiOperation("Export  uservssubsource report ", "")]
        public async Task<Response<string>> GetReportAsync([FromQuery] CreateExcelForUserVsSubSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPost("uservssubsource/export/email")]
        [TenantIdHeader]
        [OpenApiOperation("Export uservssubsource report by email", "")]
        public async Task<Response<Guid>> GetReportAsync(RunAWSBatchForUserVsSubSourceReportRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("reportconfig")]
        [TenantIdHeader]
        [OpenApiOperation("Create payload for report configuration", "")]
        public async Task<Response<bool>> CreateReportConfigurationPayload(CreateReportConfigurationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("reportconfig")]
        [TenantIdHeader]
        [OpenApiOperation("Update payload for report configuration", "")]
        public async Task<Response<bool>> UpdateReportConfigurationPayload(UpdateReportConfigurationRequest request)
        {
            return await Mediator.Send(request);
        }


        [HttpDelete("reportconfig")]
        [TenantIdHeader]
        [OpenApiOperation("Delete payload for report configuration", "")]
        public async Task<Response<bool>> DeleteReportConfigurationPayload([FromQuery] DeleteReportConfigurationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpDelete("tenantrunningautomationjobs")]
        [TenantIdHeader]
        [OpenApiOperation("Delete all running automation jobs for the current tenant", "")]
        public async Task<Response<bool>> DeleteTenantAllRunningAutomationJobs()
        {
            return await Mediator.Send(new DeleteTenantRunningAutomationJobsRequest());
        }

        [HttpDelete("runningautomationjobs")]
        [TenantIdHeader]
        [OpenApiOperation("Delete all running automation jobs for all tenants", "")]
        public async Task<Response<bool>> DeleteAllRunningAutomationJobs()
        {
            return await Mediator.Send(new DeleteAllRunningAutomationJobsRequest());
        }

        [HttpGet("ReportConfigurations")]
        [TenantIdHeader]
        [OpenApiOperation("Get all report configurations", "")]
        public async Task<PagedResponse<ViewReportsConfigurationDto, string>> GetAllReportConfigurations([FromQuery] GetAllReportConfigurationsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("ReportConfiguration{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get report configurations by id", "")]
        public async Task<Response<ViewReportsConfigurationDto>> GetReportConfigurationById(Guid id)
        {
            return await Mediator.Send(new GetReportConfigurationByIdRequest(id));
        }

        [HttpPost("Reportautomation")]
        [TenantIdHeader]
        [OpenApiOperation("Get report configurations by id", "")]
        public async Task<Response<bool>> CreatReportAutomation(CreateReportAutomationRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("ReportConfiguration/export/tracker")]
        [TenantIdHeader]
        [OpenApiOperation("Get all export report configurations trackers", "")]
        public async Task<PagedResponse<ExportReportConfigurationsTrackerDto, string>> GetReportConfigurationTrackerAsync([FromQuery] GetAllReportConfigurationsTrackerRequest request)
        {
            return await Mediator.Send(request);
        }


        [HttpGet("ReportTypes")]
        [TenantIdHeader]
        [OpenApiOperation("Get all report types", "")]
        public async Task<Response<Dictionary<string, Dictionary<string, string>>>> GetReportTypesAsync()
        {
            return await Mediator.Send(new GetAllReportTypeNamesRequest());
        }

        [HttpGet("reportconfig-exist")]
        [TenantIdHeader]
        [OpenApiOperation("Cheking duplication with display name")]
        public async Task<ActionResult<Response<bool>>> CheckReportConfig([FromQuery] string displayname)
        {
            var response = await Mediator.Send(new CheckReportConfigRequest(displayname));
            return Ok(response);
        }

        [HttpGet("SalesDashBoardTracker")]
        [TenantIdHeader]
        [AllowAnonymous]
        [ApiKey]
        [OpenApiOperation("Get Sales Dashboard Tracker", "")]
        public async Task<List<SalesDashboardTrackerDto>> GetSalesDashBoardTracker([FromQuery] GetSalesDashboardTrackerRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}
