﻿using Lrb.Application.Project.Web.Requests.CommonHandler;
using Lrb.Application.Project.Web.Specs;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetProjectBlocksRequest : PaginationFilter, IRequest<PagedResponse<ViewBlockDto, string>>
    {
        public Guid ProjectId { get; set; }
    }

    public class GetProjectBlocksRequestHandler : ProjectCommonRequestHandler, IRequestHandler<GetProjectBlocksRequest, PagedResponse<ViewBlockDto, string>>
    {
        public GetProjectBlocksRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {

        }
        public async Task<PagedResponse<ViewBlockDto, string>> Handle(GetProjectBlocksRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.ListAsync(new GetProjectByIdForUnitAndBlockSpecs(request.ProjectId))).FirstOrDefault();
            if (project == null)
            {
                throw new NotFoundException("Project Not Found By Id");
            }
            var blocks = await _blockRepo.ListAsync(new GetAllProjectBlocksSpecs(request), cancellationToken);
            var count = await _blockRepo.CountAsync(new GetAllProjectBlocksSpecs(request), cancellationToken);
            var blockDtos = blocks.Adapt<List<ViewBlockDto>>();
            var fieldValues = await _dapperRepository.GetFormFieldValues(_currentUser.GetTenant() ?? string.Empty, project.Id);
            var formFieldIds = fieldValues.Select(i => i.FormFieldId).Distinct().ToList();
            foreach (var unit in blockDtos)
            {
                if (fieldValues != null)
                {
                    var unitValues = fieldValues.Where(i => i.EntityChildId == unit.Id).ToList();
                    var customfileds = formFieldIds.Select(fieldId =>
                    {
                        var value = unitValues.FirstOrDefault(v => v.FormFieldId == fieldId);

                        return new FormFieldValueDto
                        {
                            FormFieldId = fieldId,
                            EntityId = value?.EntityId,
                            EntityChildId = unit.Id,
                            Value = value?.Value
                        };
                    }).ToList();

                    unit.CustomFields = customfileds;
                }
            }
            return new(blockDtos, count);
        }
    }
}
