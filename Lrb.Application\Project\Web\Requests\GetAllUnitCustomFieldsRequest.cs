﻿
using DocumentFormat.OpenXml.Office2010.Excel;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetAllUnitCustomFieldsRequest : IRequest<Response<List<string>>>
    {
        public Guid? EntityChildId { get; set; }
        public GetAllUnitCustomFieldsRequest(Guid id)
        {
            EntityChildId = id;
        }
    }
    public class GetAllUnitCustomFieldsRequestHandler : IRequestHandler<GetAllUnitCustomFieldsRequest, Response<List<string>>>
    {
        private readonly IDapperRepository _dappperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAllUnitCustomFieldsRequestHandler(IDapperRepository dappperRepository, ICurrentUser currentUser)
        {
            _dappperRepository = dappperRepository;
            _currentUser = currentUser;
        }

        public async Task<Response<List<string>>> Handle(GetAllUnitCustomFieldsRequest request, CancellationToken cancellationToken)
        {
            var formfieldsTemplate = await _dappperRepository.GetCustomFieldsNames(_currentUser.GetTenant(),request.EntityChildId);
            var headers = Enum.GetNames(typeof(UnitDataColumn)).ToList();
            headers.AddRange(formfieldsTemplate);
            return new Response<List<string>>(headers);
        }
    }

}