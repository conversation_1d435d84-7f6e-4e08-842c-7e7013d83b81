﻿using Lrb.Application.Lead.Web;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.Property.Web.V2.Dtos;
using Lrb.Application.Property.Web.V2.Requests;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Application.Property.Web.V2.Mapping
{
    public static class PropertyMappingV2
    {
        public static IRepository<MasterPropertyType> _masterPropertyTypeRepo = null;
        public static IRepository<MasterPropertyAttribute> _masterPropertyAttributeRepo = null;
        public static IRepository<MasterAreaUnit> _masterAreaUnitRepo = null;

        public static void configure(IServiceProvider serviceProvider)
        {
            _masterPropertyTypeRepo = (IRepository<MasterPropertyType>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyType)));
            _masterPropertyAttributeRepo = (IRepository<MasterPropertyAttribute>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterPropertyAttribute)));
            _masterAreaUnitRepo = (IRepository<MasterAreaUnit>)serviceProvider.GetRequiredService(typeof(IRepository<>).MakeGenericType(typeof(MasterAreaUnit)));

            List<MasterPropertyType>? propertytypes = null;
            List<MasterPropertyAttribute>? masterPropertyAttributes = null;
            if (_masterPropertyTypeRepo != null)
            {
                propertytypes = _masterPropertyTypeRepo.ListAsync().Result;
            }
            if (_masterPropertyAttributeRepo != null)
            {
                masterPropertyAttributes = _masterPropertyAttributeRepo.ListAsync(new GetAllMasterPropertyAttributeSpec()).Result;
            }
            List<MasterAreaUnit>? masterAreaUnit = null;
            if (_masterAreaUnitRepo != null)
            {
                masterAreaUnit = _masterAreaUnitRepo.ListAsync(new GetAllMasterAreaUnitSpec()).Result;
            }

            TypeAdapterConfig<Domain.Entities.Property, ViewPropertyDtoV2>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.MasterPropertyAmenityId) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.Where(i => i.GalleryType != PropertyGalleryType.Video).GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.Videos, src => src.Galleries != null && src.Galleries.Any() ? src.Galleries.Where(i => i.GalleryType == PropertyGalleryType.Video) : null)
                .Map(dest => dest.PropertyType, src => src.PropertyType != null && src.PropertyType.BaseId != null ? new PropertyTypeDto
                {
                    Id = src.PropertyType.BaseId.Value,
                    BaseId = null,
                    DisplayName = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).DisplayName,
                    Type = propertytypes.FirstOrDefault(i => i.Id == src.PropertyType.BaseId.Value).Type,
                    Level = 0,
                    ChildType = new()
                    {
                        Id = src.PropertyType.Id,
                        BaseId = src.PropertyType.BaseId,
                        DisplayName = src.PropertyType.DisplayName,
                        Level = src.PropertyType.Level,
                        Type = src.PropertyType.Type,
                        ChildType = null
                    }

                } : null)
                .Map(dest => dest.Dimension, src => src.Dimension ?? null)
                .Map(dest => dest.Dimension.Unit, src => (src.Dimension != null && (src.Dimension.AreaUnitId != null && src.Dimension.AreaUnitId != Guid.Empty) && (masterAreaUnit != null) && masterAreaUnit.Any()) ? ((masterAreaUnit.Where(i => src.Dimension.AreaUnitId == i.Id) != null && masterAreaUnit.Where(i => src.Dimension.AreaUnitId == i.Id).Any()) ? masterAreaUnit.FirstOrDefault(i => src.Dimension.AreaUnitId == i.Id).Unit : null) : null)
                .Map(dest => dest.OwnerDetails, src => src.OwnerDetails ?? null)
                .Map(dest => dest.Address, src => src.Address ?? null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                .Map(dest => dest.Project, src => src.Project != null ? src.Project.Name : null)
                .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null)
                .Map(dest => dest.DLDPermitNumber, src => src.PermitNumber)
                .Map(dest => dest.PropertyOwnerDetails, src => src.PropertyOwnerDetails ?? null);

            TypeAdapterConfig<Domain.Entities.Property, CreatePropertyDtoV2>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.Id) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())
                .Map(dest => dest.PropertyTypeId, src => src.PropertyType != null ? src.PropertyType.Id : default)
                .Map(dest => dest.PlaceId, src => src.Address != null ? src.Address.PlaceId : null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                .Map(dest => dest.DLDPermitNumber, src => src.PermitNumber);

            TypeAdapterConfig<Domain.Entities.Property, UpdatePropertyDtoV2>
                .NewConfig()
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => i.Id) : new List<Guid>())
                .Map(dest => dest.ImageUrls,
                                src => src.Galleries != null && src.Galleries.Any() ?
                                src.Galleries.GroupBy(i => i.ImageKey).ToDictionary(group => group.Key, group => group.Select(i => i).ToList())
                                : new())

                .Map(dest => dest.PropertyTypeId, src => src.PropertyType != null ? src.PropertyType.Id : default)
                .Map(dest => dest.PlaceId, src => src.Address != null ? src.Address.PlaceId : null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHK, src => src.NoOfBHKs)
                .Map(dest => dest.AssignedTo, src => src.PropertyAssignments != null ? src.PropertyAssignments.Where(i => i.IsCurrentlyAssigned).Select(i => i.AssignedTo).ToList() : null)
                .Map(dest => dest.DLDPermitNumber, src => src.PermitNumber)
                .Map(dest => dest.PropertyOwnerDetails, src => src.PropertyOwnerDetails);

            TypeAdapterConfig<CreatePropertyListingV2Request, Domain.Entities.Property>
                .NewConfig()
                .Map(dest => dest.Galleries, src => src.ImageUrls != null && src.ImageUrls.Any() ? src.ImageUrls.GetPropertyGallery() : null)
                .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => new PropertyAmenity { MasterPropertyAmenityId = i }).ToList() : null)
                .Map(dest => dest.Brochures, src => src.Brochures)
                .Map(dest => dest.NoOfBHKs, src => src.NoOfBHK)
                .Ignore(dest => dest.Project)
                .Map(dest => dest.PermitNumber, src => src.DLDPermitNumber);

            TypeAdapterConfig<UpdatePropertyListingV2Request, Domain.Entities.Property>
                    .NewConfig()
                    .Ignore(i => i.Address)
                    .Ignore(i => i.TagInfo)
                    .Ignore(i => i.PropertyOwnerDetails)
                    .Ignore(i => i.MonetaryInfo)
                    .Ignore(i => i.Dimension)
                    .Ignore(i => i.PropertyType)
                    .Ignore(i => i.Galleries)
                    .Ignore(i => i.Attributes)
                    .Ignore(i => i.Amenities)
                    .Ignore(i => i.SerialNo)
                    .Ignore(i => i.TenantContactInfo)
                    .Ignore(i => i.Compliance)
                    .Map(dest => dest.Galleries, src => src.ImageUrls != null && src.ImageUrls.Any() ? src.ImageUrls.GetPropertyGallery() : null)
                    .Map(dest => dest.Amenities, src => src.Amenities != null && src.Amenities.Any() ? src.Amenities.Select(i => new PropertyAmenity { MasterPropertyAmenityId = i }).ToList() : null)
                    .Map(dest => dest.Brochures, src => src.Brochures)
                    .Map(dest => dest.NoOfBHKs, src => src.NoOfBHK)
                    .Ignore(dest => dest.Project)
                    .Map(dest => dest.PermitNumber, src => src.DLDPermitNumber);

            TypeAdapterConfig<DataItem, ListingSourceAddressDtoV2>
                .NewConfig()
                .Map(dest => dest.City, src => (src.tree != null && src.tree.Any()) ? src.tree.Where(i => i.type == "CITY").Select(i => i.name).FirstOrDefault() : null)
                .Map(dest => dest.Community, src => (src.tree != null && src.tree.Any()) ? src.tree.Where(i => i.type == "COMMUNITY").Select(i => i.name).FirstOrDefault() : null)
                .Map(dest => dest.SubCommunity, src => (src.tree != null && src.tree.Any()) ? src.tree.Where(i => i.type == "SUBCOMMUNITY").Select(i => i.name).FirstOrDefault() : null)
                .Map(dest => dest.TowerName, src => (src.tree != null && src.tree.Any()) ? src.tree.Where(i => i.type == "TOWER").Select(i => i.name).FirstOrDefault() : null)
                .Map(dest => dest.LocationId, src => src.id)
                .Map(dest => dest.Latitude, src => src.Coordinates.lat)
                .Map(dest => dest.Longitude, src => src.Coordinates.lng);

            TypeAdapterConfig<CreateListingAddressDtoV2, ListingSourceAddress>
                .NewConfig()
                .Map(dest => dest.ListingSourceId, src => src.SourceId);

            TypeAdapterConfig<PropertyExportFilterForListingManagement, PropertyFormettedExportFilterForListingManagementV2>.NewConfig()
              .MapWith(src => new PropertyFormettedExportFilterForListingManagementV2
              {
                  UaeEmirate = src.UaeEmirate.ToString(),
                  BR = src.NoOfBHK.ToString(),
                  PropertySize = src.PropertySize != null ? src.PropertySize.Area.ToString() : null,
                  Cities = ConvertStringListToString(src.Cities),
                  EnquiredFor = src.EnquiredFor.ToString(),
                  PropertyStatus = src.PropertyStatus.ToString(),
                  PropertyTypes = ConvertListGuidetostring(src.PropertyTypes),
                  PropertySubTypes = ConvertListGuidetostring(src.PropertySubTypes),
                  Amenities = ConvertListGuidetostring(src.Amenities),
                  FromPossessionDate = src.FromPossessionDate.ToString(),
                  ToPossessionDate = src.ToPossessionDate.ToString(),
                  Projects = ConvertStringListToString(src.Projects),
                  FurnishStatuses = ConvertListEnumtoString(src.FurnishStatuses),
                  Facing = src.Facing.ToString(),
                  NoOfBathrooms = ConvertIntListToString(src.NoOfBathrooms),
                  NoOfFloor = ConvertIntListToString(src.NoOfFloor),
                  UserNames = src.UserNames != null ? src.UserNames.ToString() : null,
                  Communities = ConvertStringListToString(src.Communities),
                  SubCommunities = ConvertStringListToString(src.SubCommunities),
                  PropertyVisiblity = src.PropertyVisiblity.ToString(),
                  FirstLevelFilter = src.FirstLevelFilter.ToString(),
                  SecondLevelFilter = src.SecondLevelFilter.ToString(),
                  CompletionStatus = src.CompletionStatus.ToString(),
                  ListingLevel = src.ListingLevel.ToString(),
                  ListingSourceIds = ConvertListGuidetostring(src.ListingSourceIds),
                  DeveloperName = ConvertStringListToString(src.DeveloperName), 
              });
        }

        public static List<PropertyGallery>? GetPropertyGallery(this Dictionary<string, List<PropertyGalleryDtoV2>>? imageUrls)
        {
            if (imageUrls == null) return null;
            List<PropertyGallery> galleries = new List<PropertyGallery>();
            if (imageUrls != null && imageUrls.Any())
            {
                foreach (var group in imageUrls.Where(i => i.Value.Any(i => !string.IsNullOrWhiteSpace(i.ImageFilePath))))
                {
                    List<PropertyGalleryDtoV2> images = group.Value;
                    if (images != null && images.Any())
                    {
                        foreach (var image in images)
                        {
                            galleries.Add(new PropertyGallery()
                            {
                                ImageKey = string.IsNullOrWhiteSpace(group.Key) ? "default" : group.Key,
                                ImageFilePath = image?.ImageFilePath ?? string.Empty,
                                IsCoverImage = image?.IsCoverImage ?? false,
                                Height = image?.Height,
                                Width = image?.Width,
                            });
                        }
                    }
                }
            }
            return galleries;
        }

        private static string ConvertListEnumtoString(List<BHKType>? bHKTypes)
        {
            if (bHKTypes == null || bHKTypes.Count == 0)
                return null;
            List<string> enumStringList = bHKTypes.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }



        private static string ConvertListGuidetostring(List<Guid>? propertyTypes)
        {
            if (propertyTypes == null || propertyTypes.Count == 0)
            {
                return null;
            }



            return string.Join(",", propertyTypes.Select(g => g.ToString()));
        }

        private static string ConvertListEnumtoString(List<FurnishStatus>? furnishStatuses)
        {
            if (furnishStatuses == null || furnishStatuses.Count == 0)
                return null;



            List<string> enumStringList = furnishStatuses.Select(e => e.ToString()).ToList();
            return string.Join(", ", enumStringList);
        }



        private static string ConvertIntListToString(List<int> intList)
        {
            if (intList == null || intList.Count == 0)
            {
                return "";
            }



            string result = string.Join(", ", intList.Select(x => x.ToString()));
            return result;
        }



        private static string ConvertStringListToString(List<string>? locations)
        {
            if (locations == null || locations.Count == 0)
                return null;
            List<string> forstringsList = locations.Select(e => e.ToString()).ToList();
            return string.Join(", ", forstringsList);
        }
    }
}
