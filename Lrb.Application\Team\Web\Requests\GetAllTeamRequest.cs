﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Specs;
using Mapster;

namespace Lrb.Application.Team.Web
{
    public class GetAllTeamRequest : PaginationFilter, IRequest<PagedResponse<TeamDto, string>>
    {
        public string? SearchTeamName { get; set; }
        public class GetAllTeamRequestHandler : IRequestHandler<GetAllTeamRequest, PagedResponse<TeamDto, string>>
        {
            private readonly IReadRepository<Domain.Entities.Team> _teamRepository;
            private readonly IReadRepository<Domain.Entities.Lead> _leadRepo;
            private readonly IReadRepository<Domain.Entities.UserView> _userView;
            private readonly IUserService _userService;
            public GetAllTeamRequestHandler(IReadRepository<Domain.Entities.Team> teamRepository,
                IReadRepository<Domain.Entities.Lead> leadRepo,
                IReadRepository<UserView> userView,
                IUserService userService)
            {
                _teamRepository = teamRepository;
                _leadRepo = leadRepo;
                _userView = userView;
                _userService = userService;
            }

            public async Task<PagedResponse<TeamDto, string>> Handle(GetAllTeamRequest request, CancellationToken cancellationToken)
            {
                try
                {
                    var teams = await _teamRepository.ListAsync(new TeamByNameCustomFilterSpec(request), cancellationToken) ??  throw new NotFoundException("No teams are there!");
                    var totalCount = teams.Count();

                List<TeamDto> allTeams = new List<TeamDto>();
                List<Guid> allUserIds = new();
                    teams.ForEach(i => {
                    allUserIds.AddRange(i.UserIds ?? new List<Guid>());
                    });
                var allManagerIds = teams.Select(i => (i.Manager ?? Guid.Empty)).ToList();
                if (allManagerIds?.Any() ?? false)
                {
                    allUserIds.AddRange(allManagerIds);
                }
                allUserIds = allUserIds.Where(i => i != Guid.Empty).ToList();
                var allUsers = await _userView.ListAsync(new GetUserByIdsSpec(allUserIds ?? new()));

                    foreach (var team in teams)
                    {
                        var teamUsers = allUsers.Where(i => team.UserIds != null && team.UserIds.Contains(i.Id)).ToList().OrderBy(u => team.UserIds.IndexOf(u.Id)).ToList(); 
                        var teamManager = allUsers.Where(i => i.Id == team.Manager).FirstOrDefault();
                        TeamDto teamDto = team.Adapt<TeamDto>();
                        teamDto.TeamName = team?.Name;
                        teamDto.TotalMembers = team?.UserIds?.Count() ?? 0;
                        teamDto.LeadsAssigned = await _leadRepo.CountAsync(new LeadsCountByUserIdSpec(team?.UserIds ?? new List<Guid>()), cancellationToken);
                        teamDto.ImageUrl = teamManager?.ImageUrl;
                        var TeamUsersDto = teamUsers;
                        teamDto.Users = teamUsers.Adapt<List<TeamUserDto>>();
                        teamDto.Manager = teamManager?.Adapt<TeamUserDto>();
                        allTeams.Add(teamDto);
                    }
                return new PagedResponse<TeamDto, string>(allTeams, totalCount);
                }
                catch(Exception ex) 
                {
                    throw new Exception(ex.Message, ex);
                }
            }
        }
    }
}