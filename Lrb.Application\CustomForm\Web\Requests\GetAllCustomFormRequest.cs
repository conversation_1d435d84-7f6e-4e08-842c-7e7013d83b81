using Lrb.Application.CustomForm.Web.Dtos;
using Lrb.Application.CustomForm.Web.Specs;

namespace Lrb.Application.CustomForm.Web.Requests
{
    public class GetAllCustomFormRequest : PaginationFilter,IRequest<PagedResponse<ViewCustomFormDto, string>>
    {
    }

    public class GetAllCustomFormRequestHandler : IRequestHandler<GetAllCustomFormRequest, PagedResponse<ViewCustomFormDto, string>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;

        public GetAllCustomFormRequestHandler(IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo)
        {
            _customFormRepo = customFormRepo;
        }

        public async Task<PagedResponse<ViewCustomFormDto, string>> Handle(GetAllCustomFormRequest request, CancellationToken cancellationToken)
        {
            var customForms = await _customFormRepo.ListAsync(new GetAllCustomFormSpecs(request), cancellationToken);
            var count = await _customFormRepo.CountAsync(new GetAllCustomFormCountSpecs(request), cancellationToken);

            var result = customForms.Adapt<List<ViewCustomFormDto>>();
            return new(result, count);
        }
    }
}
