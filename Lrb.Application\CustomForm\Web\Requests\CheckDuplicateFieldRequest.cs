﻿using Lrb.Application.CustomForm.Web.Specs;

namespace Lrb.Application.CustomForm.Web.Requests
{
    public class CheckDuplicateFieldRequest : IRequest<Response<bool>>
    {
        public string? FieldDisplayName { get; set; }
        public string? Module { get; set; } = default!;
        public Guid? EntityId { get; set; }
        public Guid? EntityChildId { get; set; }
    }
    public class CheckDuplicateFieldRequestHandler : IRequestHandler<CheckDuplicateFieldRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.CustomFormFields> _customFormRepo;
        public CheckDuplicateFieldRequestHandler(IRepositoryWithEvents<Domain.Entities.CustomFormFields> customFormRepo)
        {
            _customFormRepo = customFormRepo;
        }
        public async Task<Response<bool>> Handle(CheckDuplicateFieldRequest request, CancellationToken cancellationToken)
        {
            var customForms = await _customFormRepo.ListAsync(new GetAllCustomFormCountSpecs(), cancellationToken);
            var matchingDisplayNames = customForms.Where(cf => cf.FieldDisplayName.ToLower().Replace(" ", "").Replace("_", "").Trim() == request.FieldDisplayName?.ToLower().Replace(" ", "").Replace("_", "").Trim()).ToList();
            if (!matchingDisplayNames.Any())
            {
                return new Response<bool>(false);
            }
            bool isDuplicate = matchingDisplayNames.Any(cf => cf.Module == request.Module && cf.EntityChildId == request.EntityChildId);
            return new(isDuplicate);
        }
    }
}

