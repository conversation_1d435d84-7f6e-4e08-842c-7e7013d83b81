﻿using Amazon.Runtime.Internal.Util;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Stores;
using Lrb.Application.Common.Caching;
using Lrb.Application.Common.Exceptions;
using Lrb.Application.Common.Identity;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Cognito;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Shared.Authorization;
using Lrb.Shared.Extensions;
using Lrb.Shared.Utils;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Primitives;
using Microsoft.Graph.TermStore;
using Newtonsoft.Json;
using System.IdentityModel.Tokens.Jwt;
using System.Linq.Expressions;

namespace Lrb.Infrastructure.Middleware
{
    public class JwtMiddleware : IMiddleware
    {
        private ICognitoService<Client>? _cognitoService;

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
            try
            {
                TenantDbContext tenantDbContext = context.RequestServices?.GetService<TenantDbContext>();
                IMultiTenantStore<LrbTenantInfo> multiTenantStore =
                context.RequestServices?.GetService<IEnumerable<IMultiTenantStore<LrbTenantInfo>>>()?.ToList()?.FirstOrDefault();
                context.Request.Headers.TryGetValue("tenant", out StringValues tenantIdParam);
                var tenantId = !string.IsNullOrEmpty(tenantIdParam) ? tenantIdParam.FirstOrDefault() : "";
                if (!string.IsNullOrEmpty(tenantId))
                {
                    if (multiTenantStore is InMemoryStore<LrbTenantInfo> inMemoryStore)
                    {
                        var cachedTenants = await inMemoryStore.TryGetAsync(tenantId);
                        if (cachedTenants is null || (cachedTenants?.ValidUpto < DateTime.UtcNow))
                        {
                            var tenant = tenantDbContext?.TenantInfo?.FirstOrDefault(i => i.Id == tenantId);
                            if (tenant is not null && (cachedTenants?.ValidUpto < tenant?.ValidUpto))
                            {
                                await inMemoryStore.TryUpdateAsync(tenant);
                            }
                            else if (tenant is not null)
                            {
                                await inMemoryStore.TryAddAsync(tenant);
                            }

                        }

                    }
                }
                else
                {
                    try
                    {
                        context.Request.Query.TryGetValue("tenant", out StringValues tenantIdQuery);
                        var tenantid = !string.IsNullOrEmpty(tenantIdQuery) ? tenantIdQuery.FirstOrDefault() : "";
                        if (!string.IsNullOrEmpty(tenantid))
                        {
                            if (multiTenantStore is InMemoryStore<LrbTenantInfo> inMemoryStore)
                            {
                                var cachedTenants = await inMemoryStore.TryGetAsync(tenantid);
                                if (cachedTenants is null || (cachedTenants?.ValidUpto < DateTime.UtcNow))
                                {
                                    var tenant = tenantDbContext?.TenantInfo?.FirstOrDefault(i => i.Id == tenantid);
                                    if (tenant is not null && (cachedTenants?.ValidUpto < tenant?.ValidUpto))
                                    {
                                        await inMemoryStore.TryUpdateAsync(tenant);
                                    }
                                    else if (tenant is not null)
                                    {
                                        await inMemoryStore.TryAddAsync(tenant);
                                    }

                                }

                            }
                        }
                    }
                    catch (Exception ex) { }
                }
            }
            catch (Exception ex) { }
            if (!string.IsNullOrEmpty(token))
            {

                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = tokenHandler.ReadJwtToken(token);
                if (jwtToken != null)
                {
                    var tokenUse = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.TokenUse).Value;
                    if (tokenUse == "id")
                    {
                        await VerifyUserAsync(context, jwtToken);

                        // attach user to context on successful jwt validation
                        context.Items[ContextKeys.CognitoUserId] = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.CognitoUserId).Value;
                        context.Items[ContextKeys.TenantId] = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.TenantId).Value;
                        try
                        {
                            context.Items[ContextKeys.FirstName] = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.FirstName).Value;
                        }
                        catch { }
                        try
                        {
                            context.Items[ContextKeys.LastName] = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.LastName).Value;
                        }
                        catch
                        { }

                        try
                        {
                            context.Items[ContextKeys.UserId] = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.UserId).Value;
                        }
                        catch { }
                        try
                        {
                            context.Items[ContextKeys.UserIdGuid] = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.UserIdGuid).Value;
                        }
                        catch { }
                        context.Items[ContextKeys.PhoneNumber] = jwtToken.Claims.First(x => x.Type == IdTokenClaimsKey.PhoneNumber).Value;
                    }
                }
            }
            await next(context);

        }

        private async Task VerifyUserAsync(HttpContext context, JwtSecurityToken jwtToken)
        {
            TenantDbContext tenantDbContext = context.RequestServices?.GetService<TenantDbContext>();
            ICacheService cacheService = context.RequestServices?.GetService<ICacheService>();
            _cognitoService = context.RequestServices?.GetService<ICognitoService<Client>>();

            IMultiTenantStore<LrbTenantInfo> multiTenantStore =
                context.RequestServices?.GetService<IEnumerable<IMultiTenantStore<LrbTenantInfo>>>()?.ToList()?.FirstOrDefault();

            if (_cognitoService != null)
            {
                var validIssuer = $"https://cognito-idp.ap-south-1.amazonaws.com/{_cognitoService.GetUserPoolId()}";
                var requestedTokenIssuer = jwtToken.Claims.FirstOrDefault(x => x.Type == IdTokenClaimsKey.Issuer)?.Value ?? default;
                if (requestedTokenIssuer == validIssuer)
                {
                    var userName = jwtToken.Claims.FirstOrDefault(x => x.Type == IdTokenClaimsKey.CognitoUserId)?.Value;
                    var response = await _cognitoService.GetUserAsync(userName);
                    if (!response.Enabled)
                    {
                        throw new UnauthorizedException("User Not Active. Please contact the administrator.", ErrorActionCode.Logout);
                    }
                    //var currentPassTimeStamp = await _cognitoService.GetPasswordTimeStampAsync(userName);
                    var currentPassTimeStamp = response.UserAttributes?.FirstOrDefault(i => i.Name == "custom:pass_stamp")?.Value?.ConvertBase64ToDateTime() ?? default;
                    var requestedPassTimeStamp = jwtToken.Claims?.FirstOrDefault(x => x.Type == IdTokenClaimsKey.PassTimeStamp)?.Value?.ConvertBase64ToDateTime() ?? default;

                    if (currentPassTimeStamp != requestedPassTimeStamp)
                    {
                        throw new UnauthorizedException("Password has been changed.", ErrorActionCode.Logout);
                    }

                    var tenantId = jwtToken.Claims?.First(x => x.Type == IdTokenClaimsKey.TenantId).Value;

                    if (!string.IsNullOrWhiteSpace(tenantId))
                    {
                        List<LrbTenantInfo>? cachedTenants = new();
                        if (multiTenantStore is InMemoryStore<LrbTenantInfo> inMemoryStore)
                        {
                            cachedTenants = (await inMemoryStore.GetAllAsync()).ToList();
                            if (!cachedTenants.Any())
                            {
                                cachedTenants = tenantDbContext?.TenantInfo.ToList();
                                foreach (var tenant in cachedTenants)
                                {
                                    await inMemoryStore.TryAddAsync(tenant);
                                }
                            }
                        }
                        if (cachedTenants != null)
                        {
                            if (cachedTenants?.Where(i => i.Id == tenantId).FirstOrDefault()?.ValidUpto < DateTime.UtcNow)
                            {
                                throw new UnauthorizedException("Tenant validity has been expired.", ErrorActionCode.Logout);
                            }
                        }
                    }
                    var IsNotAllowed = await _cognitoService.GetUserShiftTimeAsync(response, userName);
                    if (IsNotAllowed == true)
                    {
                        throw new UnauthorizedException(StringExtentions.NotAllowed, ErrorActionCode.Logout);
                    }
                    var isLocked = await _cognitoService.GetUserLockedInfoAsync(response, userName);
                    if(isLocked == true)
                    {
                        throw new UnauthorizedException(StringExtentions.UserLocked, ErrorActionCode.Logout);
                    }
                }
            }
        }

    }
}
