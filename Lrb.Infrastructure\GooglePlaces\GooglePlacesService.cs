﻿using GoogleApi.Entities.Common;
using GoogleApi.Entities.Common.Enums;
using GoogleApi.Entities.Maps.Geocoding;
using GoogleApi.Entities.Places.AutoComplete.Request.Enums;
using GoogleApi.Entities.Places.AutoComplete.Response;
using GoogleApi.Entities.Places.Common;
using GoogleApi.Entities.Places.Details.Response;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Domain.Entities;
using Microsoft.Extensions.Options;
using System.Threading;

namespace Lrb.Infrastructure.GooglePlaces
{
    public class GooglePlacesService : IGooglePlacesService
    {
        private readonly GooglePlacesSettings _settings;

        public GooglePlacesService(IOptions<GooglePlacesSettings> options)
        {
            _settings = options.Value;
        }
        public async Task<List<AutocompleteLocationModel>> GetCityByNameAutocompleteResultAsync(string inputText)
        {
            var result = await GoogleApi.GooglePlaces.AutoComplete.QueryAsync(new GoogleApi.Entities.Places.AutoComplete.Request.PlacesAutoCompleteRequest()
            {
                Key = _settings.GoogleApiKey,
                Language = Language.English,
                Input = inputText,
                Types = new List<RestrictPlaceType>() { RestrictPlaceType.Cities }
            });
            List<AutocompleteLocationModel> predictionResults = new List<AutocompleteLocationModel>();

            foreach (var prediction in result.Predictions.Where(i => i.Description.Contains("India")))
            {
                var allTerms = prediction.Terms.ToList();
                var termsCount = prediction.Terms.Count();
                if (termsCount >= 3)
                {
                    var formattedPrediction = new AutocompleteLocationModel()
                    {
                        PlaceId = prediction.PlaceId,
                        LocalityDisplayText = prediction.StructuredFormatting.MainText,
                        LocalitySubtitleText = prediction.StructuredFormatting.SecondaryText,
                        City = allTerms[termsCount - 3].Value,
                        State = allTerms[termsCount - 2].Value,
                        Country = allTerms[termsCount - 1].Value
                    };
                    predictionResults.Add(formattedPrediction);
                }
                else if (termsCount >= 2)
                {
                    var formattedPrediction = new AutocompleteLocationModel()
                    {
                        PlaceId = prediction.PlaceId,
                        LocalityDisplayText = prediction.StructuredFormatting.MainText,
                        LocalitySubtitleText = prediction.StructuredFormatting.SecondaryText,
                        State = allTerms[termsCount - 2].Value,
                        Country = allTerms[termsCount - 1].Value
                    };
                    predictionResults.Add(formattedPrediction);
                }
                else
                {
                    var formattedPrediction = new AutocompleteLocationModel()
                    {
                        PlaceId = prediction.PlaceId,
                        LocalityDisplayText = prediction.StructuredFormatting.MainText,
                        LocalitySubtitleText = prediction.StructuredFormatting.SecondaryText,
                        Country = allTerms[termsCount - 1].Value
                    };
                    predictionResults.Add(formattedPrediction);
                }
            }
            return predictionResults;
        }

        public async Task<PlaceDetailsModel> GetPlaceDetailsByPlaceIdAsync(string placeId)
        {
            PlacesDetailsResponse details = await GoogleApi.GooglePlaces.Details.QueryAsync(new GoogleApi.Entities.Places.Details.Request.PlacesDetailsRequest()
            {
                Key = _settings.GoogleApiKey,
                PlaceId = placeId,
                Language = Language.English
                //Todo - Add parameter 'Fields'
            });
            var country = (details.Result.AddressComponents.ToList()
                .Where(i => i.Types.Contains(AddressComponentType.Country)).ToList().Any()) ?
                details.Result.AddressComponents.ToList()?
                .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Country))?.LongName
                : null;
            var State = (details.Result.AddressComponents.ToList()
                .Where(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_1)).ToList().Any()) ?
                details.Result.AddressComponents.ToList()?
                .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_1))?.LongName
                 : null;
            var district = (details.Result.AddressComponents.ToList()
                .Where(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_2)).ToList().Any()) ?
                details.Result.AddressComponents.ToList()?
                .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_2))?.LongName
                : null;
            var locality = (details.Result.AddressComponents.ToList()
                .Where(i => i.Types.Contains(AddressComponentType.Locality)).ToList().Any()) ?
                details.Result.AddressComponents.ToList()?
                .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Locality))?.LongName
                : null;
            var subLocality = (details.Result.AddressComponents.ToList()
                .Where(i => i.Types.Contains(AddressComponentType.Sublocality))
                .Select(i => i.LongName).ToList().Any()) ?
                details.Result.AddressComponents.ToList()?
                .Where(i => i.Types.Contains(AddressComponentType.Sublocality))
                .Select(i => i.LongName).ToList()
                .Aggregate((x, y) => x + ',' + " " + y)
                : null;
            if (!string.IsNullOrWhiteSpace(details.Result.Name))
            {
                var name = details.Result.Name.Trim();
                if (string.IsNullOrWhiteSpace(subLocality))
                {
                    subLocality = name;
                }
                else
                {
                    subLocality = name + ", " + subLocality;
                }
            }
            var postalCode = (details.Result.AddressComponents.ToList()
                .Where(i => i.Types.Contains(AddressComponentType.Postal_Code)).ToList().Any()) ?
                details.Result.AddressComponents.ToList()?
                .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Postal_Code))?.LongName
                : null;
            var latitude = details.Result.Geometry.Location.Latitude.ToString();
            var longitude = details.Result.Geometry.Location.Longitude.ToString();
            var placeDetails = new PlaceDetailsModel()
            {
                PlaceId = placeId,
                SubLocality = subLocality,
                Locality = locality,
                City = locality,
                District = district,
                State = State,
                Country = country,
                PostalCode = postalCode,
                Latitude = latitude,
                Longitude = longitude,
                IsGoogleMapLocation = true
            };
            return placeDetails;
        }
        public async Task<List<PlaceDetailsModel>> GetPlaceDetailsByCoordinatesAsync(double lat, double lng)
        {
            List<PlaceDetailsModel> results = new();
            GeocodeResponse details = await GoogleApi.GoogleMaps.Geocode.LocationGeocode.QueryAsync(new GoogleApi.Entities.Maps.Geocoding.Location.Request.LocationGeocodeRequest()
            {
                Key = _settings.GoogleApiKey,
                Location = new Coordinate(lat, lng),
                Language = Language.English
                //Todo - Add parameter 'Fields'
            });
            double tolerance = 0.0001; // Adjust for precision
            var exactMatch = details.Results
                .FirstOrDefault(r =>
                    Math.Abs(r.Geometry.Location.Latitude - lat) <= tolerance &&
                    Math.Abs(r.Geometry.Location.Longitude - lng) <= tolerance);
            if (exactMatch != null)
            {
                var country = (exactMatch.AddressComponents.ToList()
                    .Where(i => i.Types.Contains(AddressComponentType.Country)).ToList().Any()) ?
                    exactMatch.AddressComponents.ToList()?
                    .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Country))?.LongName
                    : null;
                var State = (exactMatch.AddressComponents.ToList()
                    .Where(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_1)).ToList().Any()) ?
                    exactMatch.AddressComponents.ToList()?
                    .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_1))?.LongName
                     : null;
                var district = (exactMatch.AddressComponents.ToList()
                    .Where(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_2)).ToList().Any()) ?
                    exactMatch.AddressComponents.ToList()?
                    .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_2))?.LongName
                    : null;
                var locality = (exactMatch.AddressComponents.ToList()
                    .Where(i => i.Types.Contains(AddressComponentType.Locality)).ToList().Any()) ?
                    exactMatch.AddressComponents.ToList()?
                    .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Locality))?.LongName
                    : null;
                var subLocality = (exactMatch.AddressComponents.ToList()
                    .Where(i => i.Types.Contains(AddressComponentType.Sublocality))
                    .Select(i => i.LongName).ToList().Any()) ?
                    exactMatch.AddressComponents.ToList()?
                    .Where(i => i.Types.Contains(AddressComponentType.Sublocality))
                    .Select(i => i.LongName).ToList()
                    .Aggregate((x, y) => x + ',' + " " + y)
                    : null;
                var postalCode = (exactMatch.AddressComponents.ToList()
                    .Where(i => i.Types.Contains(AddressComponentType.Postal_Code)).ToList().Any()) ?
                    exactMatch.AddressComponents.ToList()?
                    .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Postal_Code))?.LongName
                    : null;
                var latitude = exactMatch.Geometry.Location.Latitude.ToString();
                var longitude = exactMatch.Geometry.Location.Longitude.ToString();
                var placeDetails = new PlaceDetailsModel()
                {
                    PlaceId = exactMatch.PlaceId,
                    SubLocality = subLocality,
                    Locality = locality,
                    City = locality,
                    District = district,
                    State = State,
                    Country = country,
                    PostalCode = postalCode,
                    Latitude = latitude,
                    Longitude = longitude,
                    IsGoogleMapLocation = true
                };
                results.Add(placeDetails);
            }
            else
            {
                foreach (var result in details.Results)
                {
                    var country = (result.AddressComponents.ToList()
                        .Where(i => i.Types.Contains(AddressComponentType.Country)).ToList().Any()) ?
                        result.AddressComponents.ToList()?
                        .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Country))?.LongName
                        : null;
                    var State = (result.AddressComponents.ToList()
                        .Where(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_1)).ToList().Any()) ?
                        result.AddressComponents.ToList()?
                        .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_1))?.LongName
                         : null;
                    var district = (result.AddressComponents.ToList()
                        .Where(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_2)).ToList().Any()) ?
                        result.AddressComponents.ToList()?
                        .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Administrative_Area_Level_2))?.LongName
                        : null;
                    var locality = (result.AddressComponents.ToList()
                        .Where(i => i.Types.Contains(AddressComponentType.Locality)).ToList().Any()) ?
                        result.AddressComponents.ToList()?
                        .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Locality))?.LongName
                        : null;
                    var subLocality = (result.AddressComponents.ToList()
                        .Where(i => i.Types.Contains(AddressComponentType.Sublocality))
                        .Select(i => i.LongName).ToList().Any()) ?
                        result.AddressComponents.ToList()?
                        .Where(i => i.Types.Contains(AddressComponentType.Sublocality))
                        .Select(i => i.LongName).ToList()
                        .Aggregate((x, y) => x + ',' + " " + y)
                        : null;
                    var postalCode = (result.AddressComponents.ToList()
                        .Where(i => i.Types.Contains(AddressComponentType.Postal_Code)).ToList().Any()) ?
                        result.AddressComponents.ToList()?
                        .FirstOrDefault(i => i.Types.Contains(AddressComponentType.Postal_Code))?.LongName
                        : null;
                    var latitude = result.Geometry.Location.Latitude.ToString();
                    var longitude = result.Geometry.Location.Longitude.ToString();
                    var placeDetails = new PlaceDetailsModel()
                    {
                        PlaceId = result.PlaceId,
                        SubLocality = subLocality,
                        Locality = locality,
                        City = locality,
                        District = district,
                        State = State,
                        Country = country,
                        PostalCode = postalCode,
                        Latitude = latitude,
                        Longitude = longitude,
                        IsGoogleMapLocation = true
                    };
                    results.Add(placeDetails);
                }
            }
            return results;
        }

        public async Task<List<AutocompleteLocationModel>> GetPlacesAutocompleteResultAsync(string inputText, string country)
        {
            try
            {
                List<PlacesAutoCompleteResponse> placesAutoCompleteResponses = new();
                var placeTypes = new List<RestrictPlaceType>() { RestrictPlaceType.Address, RestrictPlaceType.Regions, RestrictPlaceType.Establishment, RestrictPlaceType.Cities, RestrictPlaceType.Geocode };
                foreach (var placeType in placeTypes)
                {
                    try
                    {
                        var res = await GoogleApi.GooglePlaces.AutoComplete.QueryAsync(new GoogleApi.Entities.Places.AutoComplete.Request.PlacesAutoCompleteRequest()
                        {
                            Key = _settings.GoogleApiKey,
                            Language = Language.English,
                            Input = inputText,
                            Types = new List<RestrictPlaceType>() { placeType }

                        });
                        placesAutoCompleteResponses.Add(res);
                    }
                    catch
                    {
                    }
                }
                if(country == null)
                {
                    country = "India";
                }

                List<AutocompleteLocationModel> predictionResults = new List<AutocompleteLocationModel>();
                List<Prediction> predictions = new();
                List<Prediction> combinedPredictions = new();

                foreach (var place in placesAutoCompleteResponses)
                {
                    predictions.AddRange(place.Predictions);
                }
                if (predictions.Any())
                {
                    var countryMatches = predictions.Where(p =>  p.Description.Contains(country ?? string.Empty, StringComparison.OrdinalIgnoreCase)).ToList() ?? new List<Prediction>();
                    var otherMatches = predictions.Where(p => !(p.Description.Contains(country ?? string.Empty, StringComparison.OrdinalIgnoreCase)))?.ToList();

                    if (otherMatches?.Any() ?? false)
                    {
                        combinedPredictions = countryMatches.Concat(otherMatches).ToList();
                    }
                    else
                    {
                        combinedPredictions = countryMatches;
                    }
                }
                foreach (var prediction in combinedPredictions)
                {
                    var allTerms = prediction.Terms.ToList();
                    var termsCount = prediction.Terms.Count();
                    if (termsCount >= 3)
                    {
                        var formattedPrediction = new AutocompleteLocationModel()
                        {
                            PlaceId = prediction.PlaceId,
                            LocalityDisplayText = prediction.StructuredFormatting.MainText,
                            LocalitySubtitleText = prediction.StructuredFormatting.SecondaryText,
                            City = allTerms[termsCount - 3].Value,
                            State = allTerms[termsCount - 2].Value,
                            Country = allTerms[termsCount - 1].Value
                        };
                        predictionResults.Add(formattedPrediction);
                    }
                    else if (termsCount >= 2)
                    {
                        var formattedPrediction = new AutocompleteLocationModel()
                        {
                            PlaceId = prediction.PlaceId,
                            LocalityDisplayText = prediction.StructuredFormatting.MainText,
                            LocalitySubtitleText = prediction.StructuredFormatting.SecondaryText,
                            State = allTerms[termsCount - 2].Value,
                            Country = allTerms[termsCount - 1].Value
                        };
                        predictionResults.Add(formattedPrediction);
                    }
                    else
                    {
                        var formattedPrediction = new AutocompleteLocationModel()
                        {
                            PlaceId = prediction.PlaceId,
                            LocalityDisplayText = prediction.StructuredFormatting.MainText,
                            LocalitySubtitleText = prediction.StructuredFormatting.SecondaryText,
                            Country = allTerms[termsCount - 1].Value
                        };
                        predictionResults.Add(formattedPrediction);
                    }
                }
                return predictionResults;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<AutocompleteLocationModel>> GetPlacesInACityAutocompleteResultAsync(string inputText, string cityName)
        {

            var result = await GoogleApi.GooglePlaces.AutoComplete.QueryAsync(new GoogleApi.Entities.Places.AutoComplete.Request.PlacesAutoCompleteRequest()
            {
                Key = _settings.GoogleApiKey,
                Language = Language.English,
                Input = inputText,
                Types = new List<RestrictPlaceType>() { RestrictPlaceType.Regions }

            });
          
            List<AutocompleteLocationModel> predictionResults = new List<AutocompleteLocationModel>();
            foreach (var prediction in result.Predictions.Where(i => i.Description.Contains("India") && i.Description.ToLowerInvariant().Contains(cityName.ToLowerInvariant())))
            {
                var allTerms = prediction.Terms.ToList();
                var termsCount = prediction.Terms.Count();
                if (termsCount >= 3)
                {
                    var formattedPrediction = new AutocompleteLocationModel()
                    {
                        PlaceId = prediction.PlaceId,
                        LocalityDisplayText = prediction.StructuredFormatting.MainText,
                        LocalitySubtitleText = prediction.StructuredFormatting.SecondaryText,
                        City = allTerms[termsCount - 3].Value,
                        State = allTerms[termsCount - 2].Value,
                        Country = allTerms[termsCount - 1].Value
                    };
                    predictionResults.Add(formattedPrediction);
                }
                else if (termsCount >= 2)
                {
                    var formattedPrediction = new AutocompleteLocationModel()
                    {
                        PlaceId = prediction.PlaceId,
                        LocalityDisplayText = prediction.StructuredFormatting.MainText,
                        LocalitySubtitleText = prediction.StructuredFormatting.SecondaryText,
                        State = allTerms[termsCount - 2].Value,
                        Country = allTerms[termsCount - 1].Value
                    };
                    predictionResults.Add(formattedPrediction);
                }
                else
                {
                    var formattedPrediction = new AutocompleteLocationModel()
                    {
                        PlaceId = prediction.PlaceId,
                        LocalityDisplayText = prediction.StructuredFormatting.MainText,
                        LocalitySubtitleText = prediction.StructuredFormatting.SecondaryText,
                        Country = allTerms[termsCount - 1].Value
                    };
                    predictionResults.Add(formattedPrediction);
                }
            }
            return predictionResults;
        }
    }
}
