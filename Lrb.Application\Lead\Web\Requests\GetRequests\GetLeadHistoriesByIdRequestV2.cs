namespace Lrb.Application.Lead.Web.Requests.GetRequests
{
    public class GetLeadHistoriesByIdRequestV2 : IRequest<Response<Dictionary<DateTime, List<LeadHistoryDto>>>>
    {
        public Guid LeadId { get; set; }
        public GetLeadHistoriesByIdRequestV2(Guid id) => LeadId = id;
        public bool? CanAccessAllLeads { get; set; }
    }

    public class GetLeadHistoriesByIdRequestV2Handler : IRequestHandler<GetLeadHistoriesByIdRequestV2, Response<Dictionary<DateTime, List<LeadHistoryDto>>>>
    { 
        public GetLeadHistoriesByIdRequestV2Handler()
        {
        }
        public async Task<Response<Dictionary<DateTime, List<LeadHistoryDto>>>> Handle(GetLeadHistoriesByIdRequestV2 request, CancellationToken cancellationToken)
        {
            return new();
        }
    }
}
